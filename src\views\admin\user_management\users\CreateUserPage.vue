<template>
  <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-6">
    <!-- Enhanced <PERSON><PERSON> with MANEB Branding -->
    <div class="mb-8">
      <nav class="flex mb-5" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 text-sm font-medium md:space-x-2">
          <li class="inline-flex items-center">
            <router-link
              to="/admin/dashboard"
              class="inline-flex items-center text-gray-700 hover:text-maneb-primary transition-colors duration-200"
            >
              <svg class="w-5 h-5 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
              </svg>
              Admin Dashboard
            </router-link>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <router-link
                to="/admin/user-management/users"
                class="ml-1 text-gray-700 hover:text-maneb-primary md:ml-2 transition-colors duration-200"
              >
                User Management
              </router-link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="ml-1 text-gray-400 md:ml-2" aria-current="page">Create User</span>
            </div>
          </li>
        </ol>
      </nav>

      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Create New User</h1>
          <p class="mt-2 text-sm text-gray-600">Add a new user to the MANEB Education Management System</p>
        </div>

        <!-- MANEB Logo/Branding -->
        <div class="hidden md:flex items-center space-x-3">
          <div class="flex items-center space-x-2 px-3 py-2 bg-red-50 rounded-lg border border-red-100">
            <div class="w-8 h-8 bg-maneb-primary rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/>
              </svg>
            </div>
            <span class="text-sm font-medium text-maneb-primary">MANEB EMS</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Create User Form -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-maneb-primary" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/>
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">User Information</h3>
            <p class="mt-1 text-sm text-gray-600">Please fill in all required fields to create a new user account.</p>
          </div>
        </div>
      </div>

      <form @submit.prevent="handleSubmit" class="px-6 py-6 space-y-8">
        <!-- Personal Information Section -->
        <div class="space-y-6">
          <div class="border-l-4 border-maneb-primary pl-4">
            <h4 class="text-lg font-semibold text-gray-900">Personal Information</h4>
            <p class="text-sm text-gray-600 mt-1">Basic personal details for the user account</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- First Name -->
            <div class="space-y-2">
              <label for="firstName" class="block text-sm font-medium text-gray-700">
                First Name <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  id="firstName"
                  v-model="formData.firstName"
                  type="text"
                  required
                  :class="[
                    'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                    'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                    errors.firstName
                      ? 'border-red-300 bg-red-50 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                  ]"
                  placeholder="Enter first name"
                />
                <div v-if="errors.firstName" class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                  </svg>
                </div>
              </div>
              <p v-if="errors.firstName" class="text-sm text-red-600">{{ errors.firstName }}</p>
            </div>

            <!-- Last Name -->
            <div class="space-y-2">
              <label for="lastName" class="block text-sm font-medium text-gray-700">
                Last Name <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  id="lastName"
                  v-model="formData.lastName"
                  type="text"
                  required
                  :class="[
                    'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                    'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                    errors.lastName
                      ? 'border-red-300 bg-red-50 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                  ]"
                  placeholder="Enter last name"
                />
                <div v-if="errors.lastName" class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                  </svg>
                </div>
              </div>
              <p v-if="errors.lastName" class="text-sm text-red-600">{{ errors.lastName }}</p>
            </div>

            <!-- Gender -->
            <div class="space-y-2">
              <label for="gender" class="block text-sm font-medium text-gray-700">Gender</label>
              <select
                id="gender"
                v-model="formData.gender"
                class="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary"
              >
                <option value="">Select gender</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
              </select>
            </div>

            <!-- Date of Birth -->
            <div class="space-y-2">
              <label for="dateOfBirth" class="block text-sm font-medium text-gray-700">Date of Birth</label>
              <input
                id="dateOfBirth"
                v-model="formData.dateOfBirth"
                type="date"
                class="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary"
              />
            </div>

            <!-- ID Type -->
            <div class="space-y-2">
              <label for="idType" class="block text-sm font-medium text-gray-700">ID Type</label>
              <select
                id="idType"
                v-model="formData.idType"
                class="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary"
              >
                <option value="">Select ID type</option>
                <option value="National ID">National ID</option>
                <option value="Passport">Passport</option>
                <option value="Driver's License">Driver's License</option>
                <option value="Student ID">Student ID</option>
                <option value="Other">Other</option>
              </select>
            </div>

            <!-- ID Number -->
            <div class="space-y-2">
              <label for="idNumber" class="block text-sm font-medium text-gray-700">ID Number</label>
              <input
                id="idNumber"
                v-model="formData.idNumber"
                type="text"
                class="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 placeholder-gray-500 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary"
                placeholder="Enter ID number"
              />
            </div>
          </div>
        </div>

        <!-- Account Information Section -->
        <div class="space-y-6">
          <div class="border-l-4 border-blue-500 pl-4">
            <h4 class="text-lg font-semibold text-gray-900">Account Information</h4>
            <p class="text-sm text-gray-600 mt-1">Login credentials and system access details</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Email -->
            <div class="space-y-2">
              <label for="email" class="block text-sm font-medium text-gray-700">
                Email Address <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  id="email"
                  v-model="formData.email"
                  type="email"
                  required
                  :class="[
                    'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                    'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                    errors.email
                      ? 'border-red-300 bg-red-50 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                  ]"
                  placeholder="Enter email address"
                />
                <div v-if="errors.email" class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                  </svg>
                </div>
              </div>
              <p v-if="errors.email" class="text-sm text-red-600">{{ errors.email }}</p>
            </div>

            <!-- Username -->
            <div class="space-y-2">
              <label for="userName" class="block text-sm font-medium text-gray-700">
                Username <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  id="userName"
                  v-model="formData.userName"
                  type="text"
                  required
                  :class="[
                    'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                    'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                    errors.userName
                      ? 'border-red-300 bg-red-50 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                  ]"
                  placeholder="Enter username"
                />
                <div v-if="errors.userName" class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                  </svg>
                </div>
              </div>
              <p v-if="errors.userName" class="text-sm text-red-600">{{ errors.userName }}</p>
              <p class="text-xs text-gray-500">Username must be at least 3 characters long</p>
            </div>

            <!-- Initial Status -->
            <div class="space-y-2">
              <label for="status" class="block text-sm font-medium text-gray-700">Initial Status</label>
              <select
                id="status"
                v-model="formData.status"
                class="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary"
              >
                <option value="Unapproved">Unapproved (Default)</option>
                <option value="Approved">Approved</option>
                <option value="SecondApproved">Second Approved</option>
              </select>
              <p class="text-xs text-gray-500">Users typically start as Unapproved and require approval</p>
            </div>

            <!-- Role Assignment -->
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <label for="roleId" class="block text-sm font-medium text-gray-700">
                  Role Assignment
                  <span class="text-xs text-orange-600 font-normal">(One role per user)</span>
                </label>
                <button
                  type="button"
                  @click="fetchRoles"
                  :disabled="isRefreshingRoles"
                  class="inline-flex items-center px-2 py-1 text-xs font-medium text-maneb-primary hover:text-red-700 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Refresh roles list"
                >
                  <svg v-if="!isRefreshingRoles" class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                  </svg>
                  <svg v-else class="animate-spin w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ isRefreshingRoles ? 'Refreshing...' : 'Refresh' }}
                </button>
              </div>
              <select
                id="roleId"
                v-model="formData.roleId"
                class="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary"
              >
                <option value="">Select a role (optional)</option>
                <option v-for="role in availableRoles" :key="role.id" :value="role.id">
                  {{ role.name }} ({{ role.status }})
                </option>
              </select>
              <p class="text-xs text-gray-500">
                ⚠️ Each user can only have one role. Role can be assigned later if not selected now.
              </p>
            </div>
          </div>
        </div>

        <!-- Enhanced Error Display -->
        <div v-if="submitError" class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">Error creating user</h3>
              <div class="mt-2 text-sm text-red-700">{{ submitError }}</div>
            </div>
            <div class="ml-auto pl-3">
              <button
                @click="submitError = null"
                class="inline-flex text-red-400 hover:text-red-600 focus:outline-none focus:text-red-600"
              >
                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Enhanced Form Actions -->
        <div class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-6 border-t border-gray-200">
          <router-link
            :to="{ name: 'admin.user-management.users' }"
            class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary transition-colors duration-200"
          >
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Cancel
          </router-link>
          <button
            type="submit"
            :disabled="isSubmitting"
            class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 shadow-sm"
          >
            <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else class="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/>
            </svg>
            {{ isSubmitting ? 'Creating User...' : 'Create User' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore, useRoleStore } from '@/store'
import type { CreateUserRequest, RecordStatus, RoleDto } from '@/interfaces'
import { sweetAlert } from '@/utils/sweetAlert'

// Router
const router = useRouter()

// Stores
const userStore = useUserStore()
const roleStore = useRoleStore()

// Reactive state
const formData = reactive<CreateUserRequest & {
  dateOfBirth?: string
  roleId?: string
  status?: RecordStatus
}>({
  firstName: '',
  lastName: '',
  email: '',
  userName: '',
  gender: undefined,
  dateOfBirth: undefined,
  idType: '',
  idNumber: '',
  roleId: undefined,
  status: 'Unapproved'
})

const errors = reactive<Record<string, string>>({})
const submitError = ref<string | null>(null)
const isSubmitting = ref(false)
const availableRoles = ref<RoleDto[]>([])

// Computed
const userCreateBreadcrumbs = [
  { name: 'Admin', href: '/admin', current: false },
  { name: 'User Management', href: '/admin/user-management', current: false },
  { name: 'Users', href: '/admin/user-management/users', current: false },
  { name: 'Create User', href: '', current: true },
]

// Methods
const validateForm = (): boolean => {
  // Clear previous errors
  Object.keys(errors).forEach(key => delete errors[key])

  let isValid = true

  // Required field validation
  if (!formData.firstName.trim()) {
    errors.firstName = 'First name is required'
    isValid = false
  }

  if (!formData.lastName.trim()) {
    errors.lastName = 'Last name is required'
    isValid = false
  }

  if (!formData.email?.trim()) {
    errors.email = 'Email address is required'
    isValid = false
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    errors.email = 'Please enter a valid email address'
    isValid = false
  }

  if (!formData.userName?.trim()) {
    errors.userName = 'Username is required'
    isValid = false
  } else if ((formData.userName?.length || 0) < 3) {
    errors.userName = 'Username must be at least 3 characters long'
    isValid = false
  }

  return isValid
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  try {
    isSubmitting.value = true
    submitError.value = null

    // Prepare data for submission
    const submitData: CreateUserRequest = {
      firstName: formData.firstName.trim(),
      lastName: formData.lastName.trim(),
      email: formData.email?.trim(),
      userName: formData.userName?.trim(),
      gender: formData.gender || undefined,
      dateOfBirth: formData.dateOfBirth ? new Date(formData.dateOfBirth) : undefined,
      idType: formData.idType || undefined,
      idNumber: formData.idNumber || undefined,
    }

    // Create the user
    const newUser = await userStore.createUser(submitData)

    // Handle role assignment using proper UserRoles API endpoint
    if (formData.roleId && newUser.id) {
      try {
        console.log(`Assigning role ${formData.roleId} to newly created user ${newUser.id}`)
        await userStore.addRoleToUser(newUser.id, formData.roleId)

        // Find the assigned role name for better messaging
        const assignedRole = availableRoles.value.find(role => role.id === formData.roleId)
        const roleName = assignedRole?.name || 'Selected Role'

        console.log('Role assignment successful')
        await sweetAlert.success(
          'User Created Successfully!',
          `${newUser.firstName} ${newUser.lastName} has been added to the MANEB system and automatically assigned the role "${roleName}".`
        )
      } catch (roleError: any) {
        console.error('Role assignment failed:', roleError)
        // Find the role name for error messaging
        const assignedRole = availableRoles.value.find(role => role.id === formData.roleId)
        const roleName = assignedRole?.name || 'Selected Role'

        await sweetAlert.warning(
          'User Created - Role Assignment Failed',
          `${newUser.firstName} ${newUser.lastName} has been created successfully, but the role "${roleName}" could not be assigned automatically. You will be redirected to assign the role manually.`
        )

        // Navigate to role assignment for this specific user
        router.push({
          name: 'admin.user-management.users.detail',
          params: { id: newUser.id },
          query: { assignRole: 'true' } // Flag to open role assignment
        })
        return
      }
    } else {
      await sweetAlert.success(
        'User Created Successfully!',
        `${newUser.firstName} ${newUser.lastName} has been added to the MANEB system. You can assign a role later from the user management page.`
      )
    }

    // Navigate to user detail page
    router.push({
      name: 'admin.user-management.users.detail',
      params: { id: newUser.id }
    })

  } catch (error: any) {
    submitError.value = error.message || 'Failed to create user'
    console.error('Error creating user:', error)
  } finally {
    isSubmitting.value = false
  }
}

const isRefreshingRoles = ref(false)

const fetchRoles = async () => {
  try {
    isRefreshingRoles.value = true
    await roleStore.fetchRoles()
    availableRoles.value = roleStore.roles.filter((role: RoleDto) =>
      role.status === 'Approved' || role.status === 'SecondApproved'
    )
    console.log(`Loaded ${availableRoles.value.length} available roles for user assignment`)
  } catch (error) {
    console.error('Error fetching roles:', error)
  } finally {
    isRefreshingRoles.value = false
  }
}

// Handle window focus to refresh roles (in case roles were created in another tab)
const handleWindowFocus = () => {
  fetchRoles()
}

// Lifecycle
onMounted(() => {
  fetchRoles()
  // Add event listener for window focus to refresh roles
  window.addEventListener('focus', handleWindowFocus)
})

onUnmounted(() => {
  // Clean up event listener
  window.removeEventListener('focus', handleWindowFocus)
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary-dark:hover {
  background-color: #8b2424;
}
</style>
