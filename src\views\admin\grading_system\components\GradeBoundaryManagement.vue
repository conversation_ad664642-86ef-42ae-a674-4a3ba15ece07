<template>
  <div class="space-y-6">
    <!-- Header with Actions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-semibold text-gray-900">Grade Boundary Management</h2>
          <p class="text-sm text-gray-600 mt-1">Configure grade boundaries for subjects and academic years</p>
        </div>
        <div class="flex items-center space-x-3">
          <router-link
            :to="{ name: 'admin.grading-system.grade-boundaries.comprehensive.create' }"
            class="inline-flex items-center px-4 py-2 bg-maneb-primary text-white text-sm font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-maneb-primary focus:ring-offset-2 transition-colors duration-200"
          >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Bulk Boundaries
          </router-link>
          <router-link
            :to="{ name: 'admin.grading-system.grade-boundaries.create' }"
            class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200"
          >
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Single Boundary
          </router-link>
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">Filter Grade Boundaries</h3>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Exam Type</label>
            <select
              v-model="selectedExamType"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
            >
              <option value="">All Exam Types</option>
              <option v-for="examType in EXAM_TYPES" :key="examType.value" :value="examType.value">
                {{ examType.label }}
              </option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
            <select
              v-model="selectedSubject"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
            >
              <option value="">All Subjects</option>
              <option v-for="paper in availablePapers" :key="paper.id" :value="paper.subjectCode">
                {{ paper.subjectName }} ({{ paper.subjectCode }})
              </option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Academic Year</label>
            <select
              v-model="selectedYear"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
            >
              <option value="">All Years</option>
              <option v-for="year in availableYears" :key="year" :value="year">{{ year }}</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Grade Boundaries by Subject and Year -->
    <div v-if="gradingStore.isLoading" class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
      <div class="inline-flex items-center">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-maneb-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Loading grade boundaries...
      </div>
    </div>

    <div v-else-if="filteredBoundaries.length === 0" class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center text-gray-500">
      <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
      </svg>
      <p class="text-lg font-medium text-gray-900 mb-2">No grade boundaries found</p>
      <p class="text-gray-600">Get started by adding your first grade boundary configuration.</p>
    </div>

    <div v-else class="space-y-6">
      <div v-for="group in groupedBoundaries" :key="`${group.examLevel}-${group.subjectCode}`" class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div class="flex items-center justify-between">
            <div>
              <div class="flex items-center space-x-3 mb-1">
                <h3 class="text-lg font-medium text-gray-900">{{ getSubjectName(group.subjectCode) }}</h3>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-maneb-primary text-white">
                  {{ group.examLevel }}
                </span>
              </div>
              <p class="text-sm text-gray-600">{{ group.examYear }} Academic Year</p>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500">{{ group.boundaries.length }} boundaries</span>
              <router-link
                :to="{ name: 'admin.grading-system.grade-boundaries.create' }"
                class="text-maneb-primary hover:text-red-700 text-sm font-medium transition-colors duration-200"
              >
                Add Boundary
              </router-link>
            </div>
          </div>
        </div>

        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div
              v-for="boundary in group.boundaries"
              :key="boundary.id"
              class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200"
              :class="getGradeBoundaryClass(boundary.grade)"
            >
              <div class="flex items-center justify-between mb-2">
                <div class="font-semibold text-lg">Grade {{ boundary.grade }}</div>
                <div class="flex items-center space-x-1">
                  <router-link
                    :to="{ name: 'admin.grading-system.grade-boundaries.edit', params: { id: boundary.id } }"
                    class="p-1 text-gray-400 hover:text-maneb-primary transition-colors duration-200"
                    title="Edit Boundary"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                  </router-link>
                  <button
                    @click="deleteBoundary(boundary)"
                    class="p-1 text-gray-400 hover:text-red-600 transition-colors duration-200"
                    title="Delete Boundary"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                  </button>
                </div>
              </div>
              <div class="text-sm font-medium">{{ boundary.lowerBound }} - {{ boundary.upperBound }}%</div>
              <div class="text-xs text-gray-600 mt-1">{{ getGradeDescription(boundary.grade) }}</div>
              <div class="mt-2">
                <span
                  :class="boundary.approvalStatus === 'Approved' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                >
                  {{ boundary.approvalStatus || 'Pending' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useGradingStore, useGradingApiStore } from '@/store'
import { EXAM_TYPES } from '@/utils/exam-types'
import type { GradeBoundaryDto } from '@/interfaces'
import type { GradeBoundaryDto as ExternalGradeBoundaryDto } from '@/interfaces/external/results-api.interface'

import Swal from 'sweetalert2'

// SweetAlert utility
const sweetAlert = {
  confirm: (title: string, text: string, icon: any) => Swal.fire({ title, text, icon, showCancelButton: true, confirmButtonText: 'Yes', cancelButtonText: 'No' }),
  error: (title: string, text: string) => Swal.fire({ title, text, icon: 'error' }),
  info: (title: string, text: string) => Swal.fire({ title, text, icon: 'info' }),
  toast: {
    success: (message: string) => Swal.fire({ toast: true, position: 'top-end', icon: 'success', title: message, showConfirmButton: false, timer: 3000 })
  }
}

// Stores
const gradingStore = useGradingStore()
const gradingApiStore = useGradingApiStore()

// State
const selectedExamType = ref('')
const selectedSubject = ref('')
const selectedYear = ref('')

// Computed
const availableYears = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = currentYear - 5; i <= currentYear + 2; i++) {
    years.push(i)
  }
  return years.sort((a, b) => b - a)
})

const availablePapers = computed(() => {
  // Show all papers regardless of exam type selection to allow cross-exam-type subject selection
  return gradingApiStore.papers.filter((paper: any) =>
    !selectedExamType.value || paper.examLevel === selectedExamType.value
  )
})

const filteredBoundaries = computed(() => {
  let filtered = gradingApiStore.gradeBoundaries

  // Filter by exam type
  if (selectedExamType.value) {
    filtered = filtered.filter((boundary: ExternalGradeBoundaryDto) =>
      boundary.examLevel === selectedExamType.value
    )
  }

  // Filter by subject
  if (selectedSubject.value) {
    // Since subjectCode now contains subject ID, filter by the selected subject ID
    filtered = filtered.filter((boundary: ExternalGradeBoundaryDto) =>
      boundary.subjectCode === selectedSubject.value
    )
  }

  // Filter by year (if needed - currently not implemented in API)
  if (selectedYear.value) {
    // This would need to be implemented based on how year is stored in the API
    // For now, we'll skip this filter since the API doesn't seem to have year field
  }

  // Note: Status filtering removed for simplified workflow

  return filtered
})

const groupedBoundaries = computed(() => {
  const groups: { [key: string]: { examLevel: string; subjectCode: string; examYear: string; boundaries: ExternalGradeBoundaryDto[] } } = {}

  filteredBoundaries.value.forEach((boundary: ExternalGradeBoundaryDto) => {
    const key = `${boundary.examLevel}-${boundary.subjectCode}-${boundary.examYear || '2025'}`
    if (!groups[key]) {
      groups[key] = {
        examLevel: boundary.examLevel || '',
        subjectCode: boundary.subjectCode || '',
        examYear: boundary.examYear || '2025',
        boundaries: []
      }
    }
    groups[key].boundaries.push(boundary)
  })

  // Sort boundaries within each group by grade level
  Object.values(groups).forEach(group => {
    group.boundaries.sort((a, b) => {
      const gradeOrder = { 'A': 5, 'B': 4, 'C': 3, 'D': 2, 'F': 1 }
      return gradeOrder[b.grade as keyof typeof gradeOrder] - gradeOrder[a.grade as keyof typeof gradeOrder]
    })
  })

  return Object.values(groups).sort((a, b) => {
    // First sort by year (latest first)
    const yearA = parseInt(a.examYear) || 2025
    const yearB = parseInt(b.examYear) || 2025
    if (yearA !== yearB) {
      return yearB - yearA // Latest year first
    }

    // Then sort by exam level
    if (a.examLevel !== b.examLevel) {
      return a.examLevel.localeCompare(b.examLevel)
    }

    // Finally sort by subject name
    const subjectNameA = getSubjectName(a.subjectCode)
    const subjectNameB = getSubjectName(b.subjectCode)
    return subjectNameA.localeCompare(subjectNameB)
  })
})

// Methods
const getSubjectName = (subjectCode: string): string => {
  console.log('🔍 Looking up subject name for:', subjectCode)
  console.log('📚 Available subjects:', gradingStore.subjects.length)
  console.log('📄 Available papers:', gradingApiStore.papers.length)

  // Since subjectCode now contains subject ID, look up by ID first
  const subject = gradingStore.subjects.find((s: any) => s.id === subjectCode)
  if (subject) {
    console.log('✅ Found subject by ID:', subject.name)
    return subject.name
  }

  // Fallback: try to find by code (for backward compatibility)
  const subjectByCode = gradingStore.subjects.find((s: any) => s.code === subjectCode)
  if (subjectByCode) {
    console.log('✅ Found subject by code:', subjectByCode.name)
    return subjectByCode.name
  }

  // Try to get from API papers data
  const apiPaper = gradingApiStore.papers.find((paper: any) => paper.subjectCode === subjectCode)
  if (apiPaper) {
    console.log('✅ Found subject from papers:', apiPaper.subjectName)
    return apiPaper.subjectName
  }

  // Try to find by shortName or other fields
  const subjectByShortName = gradingStore.subjects.find((s: any) => s.shortName === subjectCode)
  if (subjectByShortName) {
    console.log('✅ Found subject by shortName:', subjectByShortName.name)
    return subjectByShortName.name
  }

  console.log('❌ Subject not found, returning:', subjectCode)
  // Last resort - just return the code/ID
  return subjectCode
}

const fetchGradeBoundaries = async () => {
  try {
    // Always fetch all boundaries first, then apply client-side filtering
    // This ensures we show all data by default
    await gradingApiStore.fetchGradeBoundaries()
    console.log('✅ Grade boundaries loaded:', gradingApiStore.gradeBoundaries.length)
  } catch (error) {
    console.error('Failed to fetch grade boundaries:', error)
    await sweetAlert.error('Error', 'Failed to load grade boundaries')
  }
}

const getGradeBoundaryClass = (grade: string | undefined): string => {
  if (!grade) return 'border-gray-200 bg-gray-50'

  const classes = {
    'A': 'border-green-200 bg-green-50',
    'B': 'border-blue-200 bg-blue-50',
    'C': 'border-yellow-200 bg-yellow-50',
    'D': 'border-orange-200 bg-orange-50',
    'F': 'border-red-200 bg-red-50'
  }
  return classes[grade as keyof typeof classes] || 'border-gray-200 bg-gray-50'
}

const getGradeDescription = (grade: string | undefined): string => {
  // No predefined descriptions - users define their own
  return ''
}

const deleteBoundary = async (boundary: ExternalGradeBoundaryDto) => {
  await sweetAlert.info('Feature Not Available', 'Delete functionality will be implemented when the API endpoint becomes available.')
}

// Watchers for filter changes
watch(selectedExamType, async (newExamType) => {
  // Fetch papers when exam type changes
  if (newExamType) {
    try {
      await gradingApiStore.fetchPapers(newExamType)
    } catch (error) {
      console.error('Failed to fetch papers:', error)
    }
  }
  // Reset subject selection when exam type changes
  selectedSubject.value = ''
  // Fetch grade boundaries
  fetchGradeBoundaries()
})

watch(selectedSubject, () => {
  fetchGradeBoundaries()
})

watch(selectedYear, () => {
  fetchGradeBoundaries()
})

// Status filter removed for simplified workflow

// Debug watcher to log filter changes (console logging removed)
watch([selectedExamType, selectedSubject, selectedYear],
  () => {
    // Grade Boundary Filters Changed (console logging removed)
  }
)

// Lifecycle
onMounted(async () => {
  console.log('🚀 GradeBoundaryManagement: Component mounted')

  // Load papers from external API to get exam types and subjects
  try {
    await gradingApiStore.fetchPapers()
  } catch (error) {
    console.error('Failed to fetch papers:', error)
  }

  // Load subjects for the dropdowns (from local store)
  if (gradingStore.subjects.length === 0) {
    await gradingStore.fetchSubjects()
  }

  console.log('📊 Data loaded - Subjects:', gradingStore.subjects.length, 'Papers:', gradingApiStore.papers.length)

  // Initial load of grade boundaries from external API
  await fetchGradeBoundaries()
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.hover\:text-maneb-primary:hover {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>
