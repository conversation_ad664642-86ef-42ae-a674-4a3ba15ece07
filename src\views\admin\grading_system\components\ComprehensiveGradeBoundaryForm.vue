<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" v-if="isOpen">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
      <!-- Header -->
      <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">
          Setup Comprehensive Grade Boundaries
        </h3>
        <button
          @click="closeModal"
          class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Form Content -->
      <div class="mt-6">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Basic Information Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Exam Type -->
            <div>
              <label for="examType" class="block text-sm font-medium text-gray-700 mb-2">
                Exam Type <span class="text-red-500">*</span>
              </label>
              <select
                id="examType"
                v-model="formData.examType"
                :disabled="isLoading"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 disabled:bg-gray-100 disabled:cursor-not-allowed"
                required
              >
                <option value="">Select Exam Type</option>
                <option v-for="examType in examTypes" :key="examType.value" :value="examType.value">
                  {{ examType.label }} - {{ examType.description }}
                </option>
              </select>
            </div>

            <!-- Exam Year -->
            <div>
              <label for="year" class="block text-sm font-medium text-gray-700 mb-2">
                Exam Year <span class="text-red-500">*</span>
              </label>
              <select
                id="year"
                v-model="formData.year"
                :disabled="isLoading"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 disabled:bg-gray-100 disabled:cursor-not-allowed"
                required
              >
                <option value="">Select Year</option>
                <option v-for="year in availableYears" :key="year" :value="year">
                  {{ year }}
                </option>
              </select>
            </div>

            <!-- Subject -->
            <div>
              <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                Subject <span class="text-red-500">*</span>
              </label>
              <select
                id="subject"
                v-model="formData.subjectId"
                :disabled="isLoading"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 disabled:bg-gray-100 disabled:cursor-not-allowed"
                required
              >
                <option value="">Select Subject</option>
                <option v-for="subject in gradingStore.activeSubjects" :key="subject.id" :value="subject.id">
                  {{ subject.name }} ({{ subject.code }})
                </option>
              </select>
            </div>


          </div>

          <!-- Grade Boundaries Section -->
          <div class="border-t border-gray-200 pt-6" v-if="currentGradingSystem">
            <div class="mb-6">
              <h4 class="text-lg font-medium text-gray-900 mb-2">Grade Boundaries</h4>
              <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h5 class="text-sm font-medium text-blue-800">{{ currentGradingSystem.name }}</h5>
                    <p class="text-sm text-blue-700">{{ currentGradingSystem.description }}</p>
                    <p class="text-xs text-blue-600 mt-1">
                      Set the score ranges for each grade level. Ensure ranges don't overlap and cover the full 0-100 range.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4" v-if="availableGrades.length > 0">
              <!-- Dynamic Grade Boundaries -->
              <div
                v-for="grade in availableGrades"
                :key="grade.value"
                :class="[
                  'border rounded-lg p-4',
                  `border-${getGradeColor(grade.value)}-200`,
                  `bg-${getGradeColor(grade.value)}-50`
                ]"
              >
                <div class="flex items-center justify-between mb-3">
                  <h5 :class="[
                    'font-semibold',
                    `text-${getGradeColor(grade.value)}-800`
                  ]">
                    {{ grade.label }}
                  </h5>
                  <span :class="[
                    'text-xs px-2 py-1 rounded-full',
                    `bg-${getGradeColor(grade.value)}-100`,
                    `text-${getGradeColor(grade.value)}-700`
                  ]">
                    {{ grade.typicalRange }}
                  </span>
                </div>

                <div class="space-y-3" v-if="formData.gradeBoundaries[grade.value]">
                  <div>
                    <label :class="[
                      'block text-xs font-medium mb-1',
                      `text-${getGradeColor(grade.value)}-700`
                    ]">
                      Min Score
                    </label>
                    <input
                      type="number"
                      v-model.number="formData.gradeBoundaries[grade.value].minScore"
                      min="0"
                      max="100"
                      :class="[
                        'w-full text-sm border rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary',
                        `border-${getGradeColor(grade.value)}-300`
                      ]"
                      required
                    />
                  </div>
                  <div>
                    <label :class="[
                      'block text-xs font-medium mb-1',
                      `text-${getGradeColor(grade.value)}-700`
                    ]">
                      Max Score
                    </label>
                    <input
                      type="number"
                      v-model.number="formData.gradeBoundaries[grade.value].maxScore"
                      min="0"
                      max="100"
                      :class="[
                        'w-full text-sm border rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary',
                        `border-${getGradeColor(grade.value)}-300`
                      ]"
                      required
                    />
                  </div>
                  <div>
                    <label :class="[
                      'block text-xs font-medium mb-1',
                      `text-${getGradeColor(grade.value)}-700`
                    ]">
                      Description
                    </label>
                    <input
                      type="text"
                      v-model="formData.gradeBoundaries[grade.value].description"
                      :placeholder="grade.description"
                      :class="[
                        'w-full text-sm border rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary',
                        `border-${getGradeColor(grade.value)}-300`
                      ]"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- No Exam Type Selected Message -->
            <div v-else class="text-center py-8">
              <div class="text-gray-400 mb-2">
                <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <p class="text-sm text-gray-500">Select an exam type to configure grade boundaries</p>
            </div>
          </div>

          <!-- No Grading System Available -->
          <div v-else-if="formData.examType && !currentGradingSystem" class="border-t border-gray-200 pt-6">
            <div class="text-center py-8">
              <div class="text-red-400 mb-2">
                <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <p class="text-sm text-red-600">No grading system configured for {{ formData.examType }}</p>
              <p class="text-xs text-gray-500 mt-1">Please contact system administrator</p>
            </div>
          </div>

          <!-- Validation Errors -->
          <div v-if="validationErrors.length > 0" class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Validation Errors</h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    <li v-for="error in validationErrors" :key="error">{{ error }}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              @click="closeModal"
              :disabled="isLoading"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="!isFormValid || isLoading"
              class="px-4 py-2 text-sm font-medium text-white bg-maneb-primary border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <span v-if="isLoading" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Creating...
              </span>
              <span v-else>Create Grade Boundaries</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useGradingStore } from '@/store'
import { EXAM_TYPES } from '@/utils/exam-types'
import type { ExamType, ComprehensiveGradeBoundaryRequest, GradeBoundarySet } from '@/interfaces'
import sweetAlert from '@/utils/sweetAlert'

// Exam-specific grade scales
interface ExamGradeLevel {
  value: string
  label: string
  description: string
  typicalRange: string
  isPass: boolean
  order: number
}

interface ExamGradingSystem {
  examType: string
  name: string
  description: string
  grades: ExamGradeLevel[]
  defaultBoundaries: Record<string, { min: number; max: number; description: string }>
}

// Define exam-specific grading systems
const EXAM_GRADING_SYSTEMS: ExamGradingSystem[] = [
  {
    examType: 'MSCE',
    name: 'MSCE Numeric Grading',
    description: 'Malawi School Certificate of Education - Numeric grades 1-9',
    grades: [
      { value: '1', label: 'Grade 1', description: 'Distinction', typicalRange: '80-100%', isPass: true, order: 1 },
      { value: '2', label: 'Grade 2', description: 'Credit', typicalRange: '70-79%', isPass: true, order: 2 },
      { value: '3', label: 'Grade 3', description: 'Credit', typicalRange: '65-69%', isPass: true, order: 3 },
      { value: '4', label: 'Grade 4', description: 'Pass', typicalRange: '60-64%', isPass: true, order: 4 },
      { value: '5', label: 'Grade 5', description: 'Pass', typicalRange: '55-59%', isPass: true, order: 5 },
      { value: '6', label: 'Grade 6', description: 'Pass', typicalRange: '50-54%', isPass: true, order: 6 },
      { value: '7', label: 'Grade 7', description: 'Fail', typicalRange: '40-49%', isPass: false, order: 7 },
      { value: '8', label: 'Grade 8', description: 'Fail', typicalRange: '30-39%', isPass: false, order: 8 },
      { value: '9', label: 'Grade 9', description: 'Fail', typicalRange: '0-29%', isPass: false, order: 9 }
    ],
    defaultBoundaries: {
      '1': { min: 80, max: 100, description: 'Distinction' },
      '2': { min: 70, max: 79, description: 'Credit' },
      '3': { min: 65, max: 69, description: 'Credit' },
      '4': { min: 60, max: 64, description: 'Pass' },
      '5': { min: 55, max: 59, description: 'Pass' },
      '6': { min: 50, max: 54, description: 'Pass' },
      '7': { min: 40, max: 49, description: 'Fail' },
      '8': { min: 30, max: 39, description: 'Fail' },
      '9': { min: 0, max: 29, description: 'Fail' }
    }
  },
  {
    examType: 'PLCE',
    name: 'PLCE Letter Grading',
    description: 'Primary Leaving Certificate Examination - Letter grades A-F',
    grades: [
      { value: 'A', label: 'Grade A', description: 'Distinction', typicalRange: '80-100%', isPass: true, order: 1 },
      { value: 'B', label: 'Grade B', description: 'Credit', typicalRange: '70-79%', isPass: true, order: 2 },
      { value: 'C', label: 'Grade C', description: 'Pass', typicalRange: '60-69%', isPass: true, order: 3 },
      { value: 'D', label: 'Grade D', description: 'Pass', typicalRange: '50-59%', isPass: true, order: 4 },
      { value: 'F', label: 'Grade F', description: 'Fail', typicalRange: '0-49%', isPass: false, order: 5 }
    ],
    defaultBoundaries: {
      'A': { min: 80, max: 100, description: 'Distinction' },
      'B': { min: 70, max: 79, description: 'Credit' },
      'C': { min: 60, max: 69, description: 'Pass' },
      'D': { min: 50, max: 59, description: 'Pass' },
      'F': { min: 0, max: 49, description: 'Fail' }
    }
  },
  {
    examType: 'TTC',
    name: 'TTC Letter Grading',
    description: 'Teacher Training Certificate - Letter grades A-F',
    grades: [
      { value: 'A', label: 'Grade A', description: 'Distinction', typicalRange: '75-100%', isPass: true, order: 1 },
      { value: 'B', label: 'Grade B', description: 'Credit', typicalRange: '65-74%', isPass: true, order: 2 },
      { value: 'C', label: 'Grade C', description: 'Pass', typicalRange: '55-64%', isPass: true, order: 3 },
      { value: 'D', label: 'Grade D', description: 'Pass', typicalRange: '45-54%', isPass: true, order: 4 },
      { value: 'F', label: 'Grade F', description: 'Fail', typicalRange: '0-44%', isPass: false, order: 5 }
    ],
    defaultBoundaries: {
      'A': { min: 75, max: 100, description: 'Distinction' },
      'B': { min: 65, max: 74, description: 'Credit' },
      'C': { min: 55, max: 64, description: 'Pass' },
      'D': { min: 45, max: 54, description: 'Pass' },
      'F': { min: 0, max: 44, description: 'Fail' }
    }
  },
  {
    examType: 'JCE',
    name: 'JCE Letter Grading',
    description: 'Junior Certificate of Education - Letter grades A-F',
    grades: [
      { value: 'A', label: 'Grade A', description: 'Distinction', typicalRange: '80-100%', isPass: true, order: 1 },
      { value: 'B', label: 'Grade B', description: 'Credit', typicalRange: '70-79%', isPass: true, order: 2 },
      { value: 'C', label: 'Grade C', description: 'Pass', typicalRange: '60-69%', isPass: true, order: 3 },
      { value: 'D', label: 'Grade D', description: 'Pass', typicalRange: '50-59%', isPass: true, order: 4 },
      { value: 'F', label: 'Grade F', description: 'Fail', typicalRange: '0-49%', isPass: false, order: 5 }
    ],
    defaultBoundaries: {
      'A': { min: 80, max: 100, description: 'Distinction' },
      'B': { min: 70, max: 79, description: 'Credit' },
      'C': { min: 60, max: 69, description: 'Pass' },
      'D': { min: 50, max: 59, description: 'Pass' },
      'F': { min: 0, max: 49, description: 'Fail' }
    }
  }
]

// Props
interface Props {
  isOpen: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false
})

// Emits
const emit = defineEmits<{
  close: []
  success: []
}>()

// Store
const gradingStore = useGradingStore()

// State
const isLoading = ref(false)
const validationErrors = ref<string[]>([])

const formData = ref<{
  examType: ExamType | ''
  subjectId: string
  year: number | ''
  gradeBoundaries: Record<string, GradeBoundarySet>
}>({
  examType: '',
  subjectId: '',
  year: '',
  gradeBoundaries: {}
})

// Computed
const examTypes = computed(() => EXAM_TYPES)

const availableYears = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = currentYear - 2; i <= currentYear + 2; i++) {
    years.push(i)
  }
  return years.sort((a, b) => b - a)
})

// Get current grading system based on selected exam type
const currentGradingSystem = computed(() => {
  if (!formData.value.examType) return null
  return EXAM_GRADING_SYSTEMS.find(system => system.examType === formData.value.examType)
})

// Get available grades for the current exam type
const availableGrades = computed(() => {
  const system = currentGradingSystem.value
  if (!system) return []
  return system.grades.sort((a, b) => a.order - b.order)
})

// Get grade colors for display
const getGradeColor = (gradeValue: string) => {
  const gradeColors: Record<string, string> = {
    'A': 'green', '1': 'green',
    'B': 'blue', '2': 'blue', '3': 'blue',
    'C': 'yellow', '4': 'yellow', '5': 'yellow', '6': 'yellow',
    'D': 'orange', '7': 'orange',
    'F': 'red', '8': 'red', '9': 'red'
  }
  return gradeColors[gradeValue] || 'gray'
}

const isFormValid = computed(() => {
  return formData.value.examType &&
         formData.value.subjectId &&
         formData.value.year &&
         validationErrors.value.length === 0
})

// Methods
const resetForm = () => {
  formData.value = {
    examType: '',
    subjectId: '',
    year: '',
    gradeBoundaries: {}
  }
  validationErrors.value = []
}

// Initialize grade boundaries based on selected exam type
const initializeGradeBoundaries = (examType: string) => {
  const system = EXAM_GRADING_SYSTEMS.find(s => s.examType === examType)
  if (!system) {
    formData.value.gradeBoundaries = {}
    return
  }

  const boundaries: Record<string, GradeBoundarySet> = {}
  system.grades.forEach(grade => {
    const defaultBoundary = system.defaultBoundaries[grade.value]
    boundaries[grade.value] = {
      gradeLevel: grade.value as any,
      minScore: defaultBoundary?.min || 0,
      maxScore: defaultBoundary?.max || 0,
      description: defaultBoundary?.description || grade.description
    }
  })

  formData.value.gradeBoundaries = boundaries
  console.log(`📊 Initialized ${Object.keys(boundaries).length} grade boundaries for ${system.name}`)
}



const validateGradeBoundaries = (): string[] => {
  const errors: string[] = []
  const boundaries = Object.values(formData.value.gradeBoundaries)

  // Check for overlapping ranges
  const sortedBoundaries = [...boundaries].sort((a, b) => b.minScore - a.minScore)
  for (let i = 0; i < sortedBoundaries.length - 1; i++) {
    const current = sortedBoundaries[i]
    const next = sortedBoundaries[i + 1]
    if (current.minScore <= next.maxScore) {
      errors.push(`Grade boundaries overlap between ${current.gradeLevel} and ${next.gradeLevel}`)
    }
  }

  // Check for gaps in coverage
  const allScores = new Set<number>()
  boundaries.forEach(boundary => {
    for (let score = boundary.minScore; score <= boundary.maxScore; score++) {
      allScores.add(score)
    }
  })

  if (allScores.size !== 101) { // Should cover 0-100 (101 values)
    errors.push('Grade boundaries must cover the complete range from 0 to 100 without gaps')
  }

  // Check individual boundary validity
  boundaries.forEach(boundary => {
    if (boundary.minScore < 0 || boundary.maxScore > 100) {
      errors.push(`Grade ${boundary.gradeLevel}: Scores must be between 0 and 100`)
    }
    if (boundary.minScore > boundary.maxScore) {
      errors.push(`Grade ${boundary.gradeLevel}: Minimum score cannot be greater than maximum score`)
    }
  })

  return errors
}

const closeModal = () => {
  resetForm()
  emit('close')
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  // Validate grade boundaries
  const errors = validateGradeBoundaries()
  if (errors.length > 0) {
    validationErrors.value = errors
    return
  }

  isLoading.value = true
  validationErrors.value = []

  try {
    const request: ComprehensiveGradeBoundaryRequest = {
      examType: formData.value.examType as ExamType,
      subjectId: formData.value.subjectId,
      year: formData.value.year as number,
      gradeBoundaries: Object.values(formData.value.gradeBoundaries),
      isActive: true
    }

    const response = await gradingStore.createComprehensiveGradeBoundaries(request)

    if (response.success) {
      await sweetAlert.toast.success(`Successfully created ${response.createdBoundaries.length} grade boundaries`)
      emit('success')
      closeModal()
    } else {
      validationErrors.value = response.errors || ['Failed to create grade boundaries']
    }
  } catch (err: any) {
    validationErrors.value = [err.message || 'Failed to create grade boundaries']
  } finally {
    isLoading.value = false
  }
}

// Watch for exam type changes to initialize appropriate grade boundaries
watch(() => formData.value.examType, (newExamType) => {
  if (newExamType) {
    initializeGradeBoundaries(newExamType)
    // Exam type changed (console logging removed)
  } else {
    formData.value.gradeBoundaries = {}
  }
})

// Watch for form changes to clear validation errors
watch(() => formData.value.gradeBoundaries, () => {
  if (validationErrors.value.length > 0) {
    validationErrors.value = []
  }
}, { deep: true })

// Reset form when modal opens
watch(() => props.isOpen, (newValue) => {
  if (newValue) {
    resetForm()
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.hover\:text-maneb-primary:hover {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>
