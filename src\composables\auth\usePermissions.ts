/**
 * @fileoverview Permission checking composables for MANEB RBAC system
 * @description Vue composables for reactive permission checking in components
 */

import { computed, type ComputedRef } from 'vue'
import { useAuthStore } from '@/store/auth.store'
import {
  PermissionModule,
  PermissionAction,
  hasPermission,
  hasModuleAccess,
  getAccessibleModules,
  getModuleActions,
  canPerformAnyAction,
  canPerformAllActions,
  getRoleConfig,
  type Permission
} from '@/utils/permissions'

/**
 * Main permission composable
 */
export function usePermissions() {
  const authStore = useAuthStore()

  // Get current user role reactively
  const currentRole = computed(() => authStore.userRole || 'unknown')

  /**
   * Check if user has a specific permission
   */
  const can = (module: PermissionModule, action: PermissionAction): ComputedRef<boolean> => {
    return computed(() => {
      if (!authStore.isAuthenticated) return false

      // First check if auth store has dynamic permission checking
      if (authStore.hasPermission) {
        return authStore.hasPermission(module, action)
      }

      // Fall back to static permission checking
      return hasPermission(currentRole.value, module, action)
    })
  }

  /**
   * Check if user has access to a module
   */
  const canAccess = (module: PermissionModule): ComputedRef<boolean> => {
    return computed(() => {
      if (!authStore.isAuthenticated) return false

      // First check if auth store has dynamic module access checking
      if (authStore.hasModuleAccess) {
        return authStore.hasModuleAccess(module)
      }

      // Fall back to static module access checking
      return hasModuleAccess(currentRole.value, module)
    })
  }

  /**
   * Check if user can perform any of the specified actions
   */
  const canAny = (module: PermissionModule, actions: PermissionAction[]): ComputedRef<boolean> => {
    return computed(() => {
      if (!authStore.isAuthenticated) return false
      return canPerformAnyAction(currentRole.value, module, actions)
    })
  }

  /**
   * Check if user can perform all of the specified actions
   */
  const canAll = (module: PermissionModule, actions: PermissionAction[]): ComputedRef<boolean> => {
    return computed(() => {
      if (!authStore.isAuthenticated) return false
      return canPerformAllActions(currentRole.value, module, actions)
    })
  }

  /**
   * Get all accessible modules for current user
   */
  const accessibleModules = computed(() => {
    if (!authStore.isAuthenticated) return []
    return getAccessibleModules(currentRole.value)
  })

  /**
   * Get all actions user can perform on a module
   */
  const getActions = (module: PermissionModule): ComputedRef<PermissionAction[]> => {
    return computed(() => {
      if (!authStore.isAuthenticated) return []
      return getModuleActions(currentRole.value, module)
    })
  }

  /**
   * Get current user's role configuration
   */
  const roleConfig = computed(() => {
    if (!authStore.isAuthenticated) return null
    return getRoleConfig(currentRole.value)
  })

  /**
   * Get all permissions for current user
   */
  const userPermissions = computed((): Permission[] => {
    if (!authStore.isAuthenticated) return []
    const config = getRoleConfig(currentRole.value)
    return config ? config.permissions : []
  })

  return {
    // Core permission checks
    can,
    canAccess,
    canAny,
    canAll,
    
    // User data
    currentRole,
    roleConfig,
    userPermissions,
    accessibleModules,
    getActions
  }
}

/**
 * Specific composables for common permission patterns
 */

/**
 * Score Entry permissions
 */
export function useScoreEntryPermissions() {
  const { can, canAccess, canAny } = usePermissions()

  return {
    canAccessScoreEntry: canAccess(PermissionModule.SCORE_ENTRY),
    canViewScores: can(PermissionModule.SCORE_ENTRY, PermissionAction.VIEW),
    canCreateScores: can(PermissionModule.SCORE_ENTRY, PermissionAction.CREATE),
    canEditScores: can(PermissionModule.SCORE_ENTRY, PermissionAction.EDIT),
    canDeleteScores: can(PermissionModule.SCORE_ENTRY, PermissionAction.DELETE),
    canApproveScores: can(PermissionModule.SCORE_ENTRY, PermissionAction.APPROVE),
    canModifyScores: canAny(PermissionModule.SCORE_ENTRY, [
      PermissionAction.CREATE,
      PermissionAction.EDIT,
      PermissionAction.DELETE
    ])
  }
}

/**
 * Results Management permissions
 */
export function useResultsPermissions() {
  const { can, canAccess, canAny } = usePermissions()

  return {
    canAccessResults: canAccess(PermissionModule.RESULTS_MANAGEMENT),
    canViewResults: can(PermissionModule.RESULTS_MANAGEMENT, PermissionAction.VIEW),
    canCreateResults: can(PermissionModule.RESULTS_MANAGEMENT, PermissionAction.CREATE),
    canEditResults: can(PermissionModule.RESULTS_MANAGEMENT, PermissionAction.EDIT),
    canDeleteResults: can(PermissionModule.RESULTS_MANAGEMENT, PermissionAction.DELETE),
    canExportResults: can(PermissionModule.RESULTS_MANAGEMENT, PermissionAction.EXPORT),
    canModifyResults: canAny(PermissionModule.RESULTS_MANAGEMENT, [
      PermissionAction.CREATE,
      PermissionAction.EDIT,
      PermissionAction.DELETE
    ])
  }
}

/**
 * User Management permissions
 */
export function useUserManagementPermissions() {
  const { can, canAccess, canAny } = usePermissions()

  return {
    canAccessUserManagement: canAccess(PermissionModule.USER_MANAGEMENT),
    canViewUsers: can(PermissionModule.USER_MANAGEMENT, PermissionAction.VIEW),
    canCreateUsers: can(PermissionModule.USER_MANAGEMENT, PermissionAction.CREATE),
    canEditUsers: can(PermissionModule.USER_MANAGEMENT, PermissionAction.EDIT),
    canDeleteUsers: can(PermissionModule.USER_MANAGEMENT, PermissionAction.DELETE),
    canManageUsers: can(PermissionModule.USER_MANAGEMENT, PermissionAction.MANAGE),
    canModifyUsers: canAny(PermissionModule.USER_MANAGEMENT, [
      PermissionAction.CREATE,
      PermissionAction.EDIT,
      PermissionAction.DELETE,
      PermissionAction.MANAGE
    ])
  }
}

/**
 * Dashboard permissions
 */
export function useDashboardPermissions() {
  const { can, canAccess } = usePermissions()

  return {
    canAccessDashboard: canAccess(PermissionModule.DASHBOARD),
    canViewDashboard: can(PermissionModule.DASHBOARD, PermissionAction.VIEW),
    canManageDashboard: can(PermissionModule.DASHBOARD, PermissionAction.MANAGE)
  }
}

/**
 * Grading permissions
 */
export function useGradingPermissions() {
  const { can, canAccess, canAny } = usePermissions()

  return {
    canAccessGrading: canAccess(PermissionModule.GRADING),
    canViewGrading: can(PermissionModule.GRADING, PermissionAction.VIEW),
    canCreateGrading: can(PermissionModule.GRADING, PermissionAction.CREATE),
    canEditGrading: can(PermissionModule.GRADING, PermissionAction.EDIT),
    canDeleteGrading: can(PermissionModule.GRADING, PermissionAction.DELETE),
    canApproveGrading: can(PermissionModule.GRADING, PermissionAction.APPROVE),
    canModifyGrading: canAny(PermissionModule.GRADING, [
      PermissionAction.CREATE,
      PermissionAction.EDIT,
      PermissionAction.DELETE
    ])
  }
}

/**
 * Reports permissions
 */
export function useReportsPermissions() {
  const { can, canAccess } = usePermissions()

  return {
    canAccessReports: canAccess(PermissionModule.REPORTS),
    canViewReports: can(PermissionModule.REPORTS, PermissionAction.VIEW),
    canCreateReports: can(PermissionModule.REPORTS, PermissionAction.CREATE),
    canExportReports: can(PermissionModule.REPORTS, PermissionAction.EXPORT)
  }
}

/**
 * System Settings permissions
 */
export function useSystemSettingsPermissions() {
  const { can, canAccess } = usePermissions()

  return {
    canAccessSystemSettings: canAccess(PermissionModule.SYSTEM_SETTINGS),
    canViewSystemSettings: can(PermissionModule.SYSTEM_SETTINGS, PermissionAction.VIEW),
    canEditSystemSettings: can(PermissionModule.SYSTEM_SETTINGS, PermissionAction.EDIT),
    canManageSystemSettings: can(PermissionModule.SYSTEM_SETTINGS, PermissionAction.MANAGE)
  }
}

/**
 * Audit Logs permissions
 */
export function useAuditLogsPermissions() {
  const { can, canAccess } = usePermissions()

  return {
    canAccessAuditLogs: canAccess(PermissionModule.AUDIT_LOGS),
    canViewAuditLogs: can(PermissionModule.AUDIT_LOGS, PermissionAction.VIEW)
  }
}
