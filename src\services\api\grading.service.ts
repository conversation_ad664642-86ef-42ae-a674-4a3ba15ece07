import type {
  GradeDto,
  SubjectDto,
  PaperDto,
  GradeBoundaryDto,
  CreateGradeRequest,
  UpdateGradeRequest,
  CreateSubjectRequest,
  UpdateSubjectRequest,
  CreatePaperRequest,
  UpdatePaperRequest,
  CreateGradeBoundaryRequest,
  UpdateGradeBoundaryRequest,
  ExamType,
  ComprehensiveGradeBoundaryRequest,
  BulkGradeBoundaryResponse
} from '@/interfaces';

// Re-export exam types from centralized utility
export { EXAM_TYPES } from '@/utils/exam-types'

/**
 * Grading Service
 *
 * Note: This service now primarily serves as a compatibility layer.
 * The grading system now uses external APIs:
 * - Papers: External papers API (http://41.79.88.105:5000/api/papers)
 * - Grade Boundaries: External grading API (http://41.79.88.105:5000/api/Grades)
 * - Scores: External scores API (http://41.79.88.105:5000/api/Scores)
 *
 * Most functionality has been moved to:
 * - gradingApiService for external API interactions
 * - gradingApiStore for state management
 */

export class GradingService {
  /**
   * @deprecated Use gradingApiService.getPapers() instead
   * This method is kept for backward compatibility only
   */
  async getAllPapers(): Promise<PaperDto[]> {
    // GradingService.getAllPapers() is deprecated (console logging removed)
    return []
  }

  /**
   * @deprecated Use gradingApiService.getPapers() with filtering instead
   * This method is kept for backward compatibility only
   */
  async getPapersBySubject(subjectId: string): Promise<PaperDto[]> {
    // GradingService.getPapersBySubject() is deprecated (console logging removed)
    return []
  }

  /**
   * @deprecated Use gradingApiService.getPapers() with filtering instead
   * This method is kept for backward compatibility only
   */
  async getPaperById(id: string): Promise<PaperDto> {
    // GradingService.getPaperById() is deprecated (console logging removed)
    throw new Error('Paper not found - use external API instead')
  }

  /**
   * @deprecated Papers are now managed through external API
   * This method is kept for backward compatibility only
   */
  async createPaper(paperData: CreatePaperRequest): Promise<PaperDto> {
    // GradingService.createPaper() is deprecated (console logging removed)
    throw new Error('Paper creation not supported - use external API instead')
  }

  /**
   * @deprecated Papers are now managed through external API
   * This method is kept for backward compatibility only
   */
  async updatePaper(id: string, paperData: UpdatePaperRequest): Promise<PaperDto> {
    // GradingService.updatePaper() is deprecated (console logging removed)
    throw new Error('Paper update not supported - use external API instead')
  }

  /**
   * @deprecated Papers are now managed through external API
   * This method is kept for backward compatibility only
   */
  async deletePaper(id: string): Promise<void> {
    // GradingService.deletePaper() is deprecated (console logging removed)
    throw new Error('Paper deletion not supported - use external API instead')
  }

  /**
   * @deprecated Subjects are now derived from papers API
   * This method is kept for backward compatibility only
   */
  async getAllSubjects(): Promise<SubjectDto[]> {
    // GradingService.getAllSubjects() is deprecated (console logging removed)
    return []
  }

  /**
   * @deprecated Subjects are now derived from papers API
   * This method is kept for backward compatibility only
   */
  async getSubjectById(id: string): Promise<SubjectDto> {
    // GradingService.getSubjectById() is deprecated (console logging removed)
    throw new Error('Subject not found - use papers API instead')
  }

  /**
   * @deprecated Subjects are now derived from papers API
   * This method is kept for backward compatibility only
   */
  async createSubject(subjectData: CreateSubjectRequest): Promise<SubjectDto> {
    // GradingService.createSubject() is deprecated (console logging removed)
    throw new Error('Subject creation not supported - subjects are derived from papers API')
  }

  /**
   * @deprecated Subjects are now derived from papers API
   * This method is kept for backward compatibility only
   */
  async updateSubject(id: string, subjectData: UpdateSubjectRequest): Promise<SubjectDto> {
    // GradingService.updateSubject() is deprecated (console logging removed)
    throw new Error('Subject update not supported - subjects are derived from papers API')
  }

  /**
   * @deprecated Subjects are now derived from papers API
   * This method is kept for backward compatibility only
   */
  async deleteSubject(id: string): Promise<void> {
    // GradingService.deleteSubject() is deprecated (console logging removed)
    throw new Error('Subject deletion not supported - subjects are derived from papers API')
  }

  /**
   * @deprecated Use gradingApiService.getGradeBoundaries() instead
   * This method is kept for backward compatibility only
   */
  async getAllGradeBoundaries(): Promise<GradeBoundaryDto[]> {
    // GradingService.getAllGradeBoundaries() is deprecated (console logging removed)
    return []
  }

  /**
   * @deprecated Use gradingApiService.getGradeBoundaries() with filtering instead
   * This method is kept for backward compatibility only
   */
  async getGradeBoundariesBySubject(subjectId: string): Promise<GradeBoundaryDto[]> {
    // GradingService.getGradeBoundariesBySubject() is deprecated (console logging removed)
    return []
  }

  /**
   * @deprecated Use gradingApiService.getGradeBoundaries() with filtering instead
   * This method is kept for backward compatibility only
   */
  async getGradeBoundariesByYear(year: number): Promise<GradeBoundaryDto[]> {
    // GradingService.getGradeBoundariesByYear() is deprecated (console logging removed)
    return []
  }

  /**
   * @deprecated Use gradingApiService.createGradeBoundary() instead
   * This method is kept for backward compatibility only
   */
  async createGradeBoundary(boundaryData: CreateGradeBoundaryRequest): Promise<GradeBoundaryDto> {
    // GradingService.createGradeBoundary() is deprecated (console logging removed)
    throw new Error('Grade boundary creation not supported - use external API instead')
  }

  /**
   * @deprecated Grade boundaries are now managed through external API
   * This method is kept for backward compatibility only
   */
  async updateGradeBoundary(id: string, boundaryData: UpdateGradeBoundaryRequest): Promise<GradeBoundaryDto> {
    // GradingService.updateGradeBoundary() is deprecated (console logging removed)
    throw new Error('Grade boundary update not supported - use external API instead')
  }

  /**
   * @deprecated Grade boundaries are now managed through external API
   * This method is kept for backward compatibility only
   */
  async deleteGradeBoundary(id: string): Promise<void> {
    // GradingService.deleteGradeBoundary() is deprecated (console logging removed)
    throw new Error('Grade boundary deletion not supported - use external API instead')
  }

  /**
   * @deprecated Use gradingApiService.createGradeBoundary() for individual boundaries instead
   * This method is kept for backward compatibility only
   */
  async createComprehensiveGradeBoundaries(request: ComprehensiveGradeBoundaryRequest): Promise<BulkGradeBoundaryResponse> {
    // GradingService.createComprehensiveGradeBoundaries() is deprecated (console logging removed)
    throw new Error('Comprehensive grade boundary creation not supported - use external API instead')
  }

  /**
   * @deprecated Use gradingApiService.getGradeBoundaries() with filtering instead
   * This method is kept for backward compatibility only
   */
  async getGradeBoundariesByExamType(examType: ExamType): Promise<GradeBoundaryDto[]> {
    // GradingService.getGradeBoundariesByExamType() is deprecated (console logging removed)
    return []
  }

  /**
   * @deprecated Use gradingApiService.getGradeBoundaries() with filtering instead
   * This method is kept for backward compatibility only
   */
  async getFilteredGradeBoundaries(filters: {
    examType?: ExamType;
    subjectId?: string;
    year?: number;
    gradeLevel?: string;
    isActive?: boolean;
  }): Promise<GradeBoundaryDto[]> {
    // GradingService.getFilteredGradeBoundaries() is deprecated (console logging removed)
    return []
  }

  // Keep grade-related methods for now as they might still be used for local calculations
  /**
   * @deprecated Grades are now calculated from scores via external API
   * This method is kept for backward compatibility only
   */
  async getAllGrades(): Promise<GradeDto[]> {
    // GradingService.getAllGrades() is deprecated (console logging removed)
    return []
  }

  /**
   * @deprecated Grades are now calculated from scores via external API
   * This method is kept for backward compatibility only
   */
  async getGradeById(id: string): Promise<GradeDto> {
    // GradingService.getGradeById() is deprecated (console logging removed)
    throw new Error('Grade not found - grades are calculated from scores via external API')
  }

  /**
   * @deprecated Grades are now calculated from scores via external API
   * This method is kept for backward compatibility only
   */
  async createGrade(gradeData: CreateGradeRequest): Promise<GradeDto> {
    // GradingService.createGrade() is deprecated (console logging removed)
    throw new Error('Grade creation not supported - grades are calculated from scores via external API')
  }

  /**
   * @deprecated Grades are now calculated from scores via external API
   * This method is kept for backward compatibility only
   */
  async updateGrade(id: string, gradeData: UpdateGradeRequest): Promise<GradeDto> {
    // GradingService.updateGrade() is deprecated (console logging removed)
    throw new Error('Grade update not supported - grades are calculated from scores via external API')
  }

  /**
   * @deprecated Grades are now calculated from scores via external API
   * This method is kept for backward compatibility only
   */
  async deleteGrade(id: string): Promise<void> {
    // GradingService.deleteGrade() is deprecated (console logging removed)
    throw new Error('Grade deletion not supported - grades are calculated from scores via external API')
  }
}

// Create singleton instance
export const gradingService = new GradingService();
