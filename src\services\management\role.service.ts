import apiClient from './api-client';
import type {
  RoleD<PERSON>,
  CreateRoleRequest,
  UpdateRoleRequest,
  RoleFilterDto,
  RolePermissionDto
} from '@/interfaces';
import { getCurrentUserId, addCreateAuditFields, addUpdateAuditFields, validateAuthentication } from '@/utils/auth-utils';

export class RoleService {
  /**
   * Check if a role is a protected system role
   */
  private isSystemAdminRole(role: RoleDto | string): boolean {
    const roleName = typeof role === 'string' ? role : role.name;
    if (!roleName) return false;

    const roleNameLower = roleName.toLowerCase();
    const systemAdminVariations = [
      'system_administrator',
      'systemadministrator',
      'sysadmin',
      'system admin',
      'sys_admin',
      'sys admin'
    ];

    return systemAdminVariations.some(variation =>
      roleNameLower.includes(variation.toLowerCase())
    );
  }

  /**
   * Get all roles
   */
  async getAllRoles(): Promise<RoleDto[]> {
    try {
      return await apiClient.get<RoleDto[]>('/api/Role');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch roles');
    }
  }

  /**
   * Get role by ID
   */
  async getRoleById(id: string): Promise<RoleDto> {
    try {
      const role = await apiClient.get<RoleDto>(`/api/Role/${id}`);
      console.log('Fetched role:', role.name, 'with', role.rolePermissions?.length || 0, 'permissions');
      return role;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch role');
    }
  }

  /**
   * Create new role
   */
  async createRole(roleData: CreateRoleRequest): Promise<RoleDto> {
    try {
      // Validate authentication
      validateAuthentication();

      const baseData = {
        ...roleData,
        status: roleData.status || 'Approved'
      };

      // Add audit fields
      const formattedData = addCreateAuditFields(baseData);

      return await apiClient.post<RoleDto>('/api/Role', formattedData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create role');
    }
  }

  /**
   * Update existing role
   */
  async updateRole(id: string, roleData: UpdateRoleRequest): Promise<RoleDto> {
    try {
      // Validate authentication
      validateAuthentication();

      // Get the current role first to preserve required fields
      const currentRole = await this.getRoleById(id);

      // Protect System Administrator role from modification
      if (this.isSystemAdminRole(currentRole)) {
        throw new Error('System Administrator role cannot be modified as it is a protected system role.');
      }

      const baseData = {
        ...currentRole,
        ...roleData,
        id: id
      };

      // Add audit fields
      const formattedData = addUpdateAuditFields(baseData, {
        createdBy: currentRole.createdBy,
        dateCreated: currentRole.dateCreated ?
          (typeof currentRole.dateCreated === 'string' ? currentRole.dateCreated : currentRole.dateCreated.toISOString()) :
          undefined
      });

      return await apiClient.put<RoleDto>(`/api/Role/${id}`, formattedData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update role');
    }
  }

  /**
   * Delete role
   */
  async deleteRole(id: string): Promise<void> {
    try {
      // Get the role first to check if it's protected
      const role = await this.getRoleById(id);

      // Protect System Administrator role from deletion
      if (this.isSystemAdminRole(role)) {
        throw new Error('System Administrator role cannot be deleted as it is a protected system role.');
      }

      await apiClient.delete(`/api/Role/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete role');
    }
  }

  /**
   * Get role permissions by role ID
   */
  async getRolePermissions(roleId: string): Promise<RolePermissionDto[]> {
    try {
      const allRolePermissions = await apiClient.get<RolePermissionDto[]>('/api/RolePermission');
      return allRolePermissions.filter(rp => rp.roleId === roleId);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch role permissions');
    }
  }

  /**
   * Get roles with pagination
   */
  async getRolesPaginated(page: number = 1, limit: number = 10): Promise<{
    roles: RoleDto[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const allRoles = await this.getAllRoles();
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      
      return {
        roles: allRoles.slice(startIndex, endIndex),
        total: allRoles.length,
        page,
        totalPages: Math.ceil(allRoles.length / limit)
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch paginated roles');
    }
  }

  /**
   * Search roles
   */
  async searchRoles(query: string): Promise<RoleDto[]> {
    try {
      const roles = await this.getAllRoles();
      const searchTerm = query.toLowerCase();
      
      return roles.filter(role => 
        role.name?.toLowerCase().includes(searchTerm) ||
        role.description?.toLowerCase().includes(searchTerm)
      );
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to search roles');
    }
  }

  /**
   * Filter roles
   */
  async filterRoles(filters: RoleFilterDto): Promise<RoleDto[]> {
    try {
      let roles = await this.getAllRoles();

      if (filters.searchQuery) {
        const searchTerm = filters.searchQuery.toLowerCase();
        roles = roles.filter(role => 
          role.name?.toLowerCase().includes(searchTerm) ||
          role.description?.toLowerCase().includes(searchTerm)
        );
      }

      if (filters.isActive !== undefined && filters.isActive !== 'All') {
        roles = roles.filter(role => role.isActive === filters.isActive);
      }

      if (filters.hasPermissions !== undefined && filters.hasPermissions !== 'All') {
        roles = roles.filter(role => {
          const hasPerms = role.rolePermissions && role.rolePermissions.length > 0;
          return filters.hasPermissions ? hasPerms : !hasPerms;
        });
      }

      // userCount filter removed as it's not part of the current interface

      return roles;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to filter roles');
    }
  }

  /**
   * Toggle role active status
   */
  async toggleRoleStatus(id: string): Promise<RoleDto> {
    try {
      return await apiClient.put<RoleDto>(`/api/Role/${id}/toggle-status`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to toggle role status');
    }
  }
}

export const roleService = new RoleService();
