/**
 * @fileoverview Navigation Permission Composable for MANEB Results Management System
 * @description Provides dynamic navigation filtering based on user role permissions
 */

import { computed } from 'vue'
import { useAuthStore } from '@/store/auth.store'
import { PermissionModule, PermissionAction } from '@/utils/permissions'

/**
 * Navigation permission composable
 * Provides methods to check navigation access based on user permissions
 */
export function useNavigationPermissions() {
  const authStore = useAuthStore()

  // Map permission modules to section IDs used in the API
  const moduleToSectionMap: Record<string, string> = {
    [PermissionModule.DASHBOARD]: 'dashboard',
    [PermissionModule.SCORE_ENTRY]: 'score_entry',
    [PermissionModule.GRADING]: 'grading',
    [PermissionModule.RESULTS_MANAGEMENT]: 'results_management',
    [PermissionModule.USER_MANAGEMENT]: 'user_management',
    [PermissionModule.REPORTS]: 'reports',
    [PermissionModule.AUDIT_LOGS]: 'audit_logs',
    [PermissionModule.SYSTEM_SETTINGS]: 'system_settings'
  }

  /**
   * Check if user has access to a specific module
   */
  const hasModulePermission = (module: PermissionModule): boolean => {
    // System Administrator has access to everything
    if (authStore.isSystemAdministrator) {
      return true
    }

    const sectionId = moduleToSectionMap[module]
    if (!sectionId) {
      console.warn(`No section mapping found for module: ${module}`)
      return false
    }

    return authStore.hasModuleAccess(sectionId)
  }

  /**
   * Get user's actions for a specific module
   */
  const getModuleActions = (module: PermissionModule): string[] => {
    // System Administrator has all actions
    if (authStore.isSystemAdministrator) {
      return ['Create', 'Read', 'Update', 'Delete', 'All']
    }

    const sectionId = moduleToSectionMap[module]
    if (!sectionId) {
      return []
    }

    return authStore.userPermissions
      .filter((p: any) => p.sectionID === sectionId && p.canAccess)
      .map((p: any) => p.action)
  }

  /**
   * Check if user has specific action permission for a module
   */
  const hasActionPermission = (module: PermissionModule, action: string): boolean => {
    // System Administrator has all permissions
    if (authStore.isSystemAdministrator) {
      return true
    }

    const sectionId = moduleToSectionMap[module]
    if (!sectionId) {
      return false
    }

    return authStore.hasPermission(sectionId, action)
  }

  /**
   * Filter navigation items based on user permissions
   */
  const filterNavigationItems = (items: any[]): any[] => {
    return items.filter(item => {
      if (!item.permission?.module) {
        // If no permission module specified, show the item
        return true
      }

      return hasModulePermission(item.permission.module)
    })
  }

  /**
   * Filter submenu items based on specific permissions
   */
  const filterSubmenuItems = (parentModule: PermissionModule, children: any[]): any[] => {
    if (!children) return []

    const userActions = getModuleActions(parentModule)

    // Enhanced permission checking for specific modules
    switch (parentModule) {
      case PermissionModule.USER_MANAGEMENT:
        return children.filter(child => {
          switch (child.name) {
            case 'Users':
              return userActions.length > 0 // Show if user has any user management permission
            case 'Roles':
              return userActions.includes('Create') || userActions.includes('Update') || userActions.includes('All')
            case 'Permissions':
              return userActions.includes('Create') || userActions.includes('Update') || userActions.includes('All')
            case 'Sections':
              return userActions.includes('Create') || userActions.includes('Update') || userActions.includes('All')
            default:
              return true
          }
        })

      case PermissionModule.SCORE_ENTRY:
        // For Score Entry, show all exam types if user has access
        return userActions.length > 0 ? children : []

      case PermissionModule.GRADING:
        // For Grading, show submenu if user has view or higher permissions
        return userActions.includes('Read') || userActions.includes('View') || userActions.includes('All') ? children : []

      default:
        // For other modules, show all children if user has any access
        return userActions.length > 0 ? children : []
    }
  }

  /**
   * Get accessible navigation modules
   */
  const accessibleModules = computed(() => {
    const modules: PermissionModule[] = []

    Object.values(PermissionModule).forEach(module => {
      if (hasModulePermission(module)) {
        modules.push(module)
      }
    })

    return modules
  })

  /**
   * Get user's permission summary for debugging
   */
  const permissionSummary = computed(() => {
    const summary: Record<string, string[]> = {}

    Object.values(PermissionModule).forEach(module => {
      if (hasModulePermission(module)) {
        summary[module] = getModuleActions(module)
      }
    })

    return summary
  })

  return {
    // Core permission checks
    hasModulePermission,
    hasActionPermission,
    getModuleActions,

    // Navigation filtering
    filterNavigationItems,
    filterSubmenuItems,

    // Computed properties
    accessibleModules,
    permissionSummary,

    // Module mapping
    moduleToSectionMap
  }
}

/**
 * Specific navigation permission checks
 */
export function useSpecificNavigationPermissions() {
  const { hasModulePermission, hasActionPermission } = useNavigationPermissions()

  return {
    // Dashboard permissions
    canAccessDashboard: computed(() => hasModulePermission(PermissionModule.DASHBOARD)),

    // Score Entry permissions
    canAccessScoreEntry: computed(() => hasModulePermission(PermissionModule.SCORE_ENTRY)),
    canCreateScores: computed(() => hasActionPermission(PermissionModule.SCORE_ENTRY, 'Create')),
    canUpdateScores: computed(() => hasActionPermission(PermissionModule.SCORE_ENTRY, 'Update')),

    // User Management permissions
    canAccessUserManagement: computed(() => hasModulePermission(PermissionModule.USER_MANAGEMENT)),
    canManageUsers: computed(() => hasActionPermission(PermissionModule.USER_MANAGEMENT, 'Create') || hasActionPermission(PermissionModule.USER_MANAGEMENT, 'Update')),
    canManageRoles: computed(() => hasActionPermission(PermissionModule.USER_MANAGEMENT, 'Create') || hasActionPermission(PermissionModule.USER_MANAGEMENT, 'Update')),

    // Grading permissions
    canAccessGrading: computed(() => hasModulePermission(PermissionModule.GRADING)),
    canManageGrading: computed(() => hasActionPermission(PermissionModule.GRADING, 'Create') || hasActionPermission(PermissionModule.GRADING, 'Update')),

    // Results Management permissions
    canAccessResults: computed(() => hasModulePermission(PermissionModule.RESULTS_MANAGEMENT)),

    // Reports permissions
    canAccessReports: computed(() => hasModulePermission(PermissionModule.REPORTS)),

    // Audit Logs permissions
    canAccessAuditLogs: computed(() => hasModulePermission(PermissionModule.AUDIT_LOGS)),

    // System Settings permissions
    canAccessSystemSettings: computed(() => hasModulePermission(PermissionModule.SYSTEM_SETTINGS)),
    canManageSystemSettings: computed(() => hasActionPermission(PermissionModule.SYSTEM_SETTINGS, 'Update') || hasActionPermission(PermissionModule.SYSTEM_SETTINGS, 'Manage'))
  }
}
