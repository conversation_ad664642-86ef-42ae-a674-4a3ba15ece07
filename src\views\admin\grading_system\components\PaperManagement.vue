<template>
  <div class="space-y-6">
    <!-- Header with Actions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-semibold text-gray-900">Paper Management</h2>
          <p class="text-sm text-gray-600 mt-1">Manage examination papers for each subject</p>
        </div>
       
      </div>

      <!-- Filters -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Search Papers</label>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search by name or code..."
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Filter by Subject</label>
          <select
            v-model="selectedSubject"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
          >
            <option value="">All Subjects</option>
            <option v-for="subject in uniqueSubjects" :key="subject.code" :value="subject.code">
              {{ subject.name }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- Papers by Subject -->
    <div v-if="gradingApiStore.isLoading" class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
      <div class="inline-flex items-center">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-maneb-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Loading papers...
      </div>
    </div>

    <div v-else-if="groupedPapers.length === 0" class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center text-gray-500">
      <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <p class="text-lg font-medium text-gray-900 mb-2">No papers found</p>
      <p class="text-gray-600">Get started by adding your first examination paper.</p>
    </div>

    <div v-else class="space-y-6">
      <div v-for="group in groupedPapers" :key="group.subjectCode" class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-medium text-gray-900">{{ group.subjectName }}</h3>
              <p class="text-sm text-gray-600">{{ group.subjectCode }}</p>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500">{{ group.papers.length }} papers</span>
              <button
                @click="addPaperToSubject(group.subjectCode)"
                class="text-maneb-primary hover:text-red-700 text-sm font-medium transition-colors duration-200"
              >
                Add Paper
              </button>
            </div>
          </div>
        </div>

        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="paper in group.papers"
              :key="paper.id"
              class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200"
            >
              <!-- Paper Header -->
              <div class="flex items-start justify-between mb-3">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
                      <svg class="w-6 h-6 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-900">{{ paper.subjectName }}</h4>
                    <p class="text-sm text-gray-600">{{ paper.paperId }}</p>
                  </div>
                </div>
                <span
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"
                >
                  {{ paper.examLevel }}
                </span>
              </div>

              <!-- Paper Details -->
              <div class="space-y-2 mb-4">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Paper Code:</span>
                  <span class="font-medium text-gray-900">{{ paper.paperId }}</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Max Marks:</span>
                  <span class="font-medium text-gray-900">{{ paper.paperMark }}</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Subject Code:</span>
                  <span class="font-medium text-gray-900">{{ paper.subjectCode }}</span>
                </div>
              </div>

              <!-- Paper Stats -->
              <div class="grid grid-cols-2 gap-2 mb-4">
                <div class="text-center p-2 bg-gray-50 rounded">
                  <div class="text-sm font-semibold text-gray-900">{{ getGradeCount(paper.id) }}</div>
                  <div class="text-xs text-gray-600">Grades</div>
                </div>
                <div class="text-center p-2 bg-gray-50 rounded">
                  <div class="text-sm font-semibold text-gray-900">{{ getBoundaryCount(paper.id) }}</div>
                  <div class="text-xs text-gray-600">Boundaries</div>
                </div>
              </div>

              <!-- Actions -->
              <div class="flex items-center justify-between pt-3 border-t border-gray-200">
                <button
                  @click="viewPaperDetails(paper)"
                  class="text-sm text-maneb-primary hover:text-red-700 font-medium transition-colors duration-200"
                >
                  View Details
                </button>
                <div class="flex items-center space-x-2">
                  <button
                    @click="editPaper(paper)"
                    class="p-2 text-gray-400 hover:text-maneb-primary transition-colors duration-200"
                    title="Edit Paper"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                  </button>
                  <button
                    @click="deletePaper(paper)"
                    class="p-2 text-gray-400 hover:text-red-600 transition-colors duration-200"
                    title="Delete Paper"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- TODO: Add Paper Modal and Details Modal components -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useGradingStore, useGradingApiStore } from '@/store'
import Swal from 'sweetalert2'

// SweetAlert utility
const sweetAlert = {
  confirm: (title: string, text: string, icon: any) => Swal.fire({ title, text, icon, showCancelButton: true, confirmButtonText: 'Yes', cancelButtonText: 'No' }),
  error: (title: string, text: string) => Swal.fire({ title, text, icon: 'error' }),
  toast: {
    success: (message: string) => Swal.fire({ toast: true, position: 'top-end', icon: 'success', title: message, showConfirmButton: false, timer: 3000 })
  }
}

// Stores
const gradingStore = useGradingStore()
const gradingApiStore = useGradingApiStore()

// State
const showPaperModal = ref(false)
const showDetailsModal = ref(false)
const selectedPaper = ref<any>(null)
const searchQuery = ref('')
const selectedSubject = ref('')
const preSelectedSubject = ref('')

// Use papers directly from the store
const filteredPapers = computed(() => {
  let filtered = gradingApiStore.papers

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter((paper: any) =>
      paper.subjectName?.toLowerCase().includes(query) ||
      paper.paperId?.toLowerCase().includes(query) ||
      paper.subjectCode?.toLowerCase().includes(query)
    )
  }

  if (selectedSubject.value) {
    filtered = filtered.filter((paper: any) => paper.subjectCode === selectedSubject.value)
  }

  return filtered
})

// Extract unique subjects from papers
const uniqueSubjects = computed(() => {
  const subjects = new Map()

  gradingApiStore.papers.forEach(paper => {
    if (!subjects.has(paper.subjectCode)) {
      subjects.set(paper.subjectCode, {
        code: paper.subjectCode,
        name: paper.subjectName
      })
    }
  })

  return Array.from(subjects.values()).sort((a, b) => a.name.localeCompare(b.name))
})

const groupedPapers = computed(() => {
  const groups: { [key: string]: { subjectCode: string; subjectName: string; papers: any[] } } = {}

  filteredPapers.value.forEach((paper: any) => {
    if (!groups[paper.subjectCode]) {
      groups[paper.subjectCode] = {
        subjectCode: paper.subjectCode,
        subjectName: paper.subjectName,
        papers: []
      }
    }
    groups[paper.subjectCode].papers.push(paper)
  })

  // Sort papers within each group by paper code
  Object.values(groups).forEach(group => {
    group.papers.sort((a: any, b: any) => a.paperId.localeCompare(b.paperId))
  })

  return Object.values(groups).sort((a, b) => {
    return a.subjectName.localeCompare(b.subjectName)
  })
})

// Methods
const getSubjectName = (subjectCode: string): string => {
  const subject = uniqueSubjects.value.find(s => s.code === subjectCode)
  return subject ? subject.name : subjectCode
}

const getSubjectCode = (subjectCode: string): string => {
  return subjectCode || 'N/A'
}

const getGradeCount = (paperId: number): number => {
  // Since the system is now subject-based, we'll return 0 for now
  // This could be updated to count grades by subject if needed
  return 0
}

const getBoundaryCount = (paperId: number): number => {
  // Since the system is now subject-based, we'll return 0 for now
  // This could be updated to count boundaries by subject if needed
  return 0
}

const formatDuration = (minutes: number): string => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  if (hours > 0) {
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`
  }
  return `${mins}m`
}

const addPaperToSubject = (subjectId: string) => {
  preSelectedSubject.value = subjectId
  selectedPaper.value = null
  showPaperModal.value = true
}

const editPaper = (paper: any) => {
  selectedPaper.value = paper
  preSelectedSubject.value = ''
  showPaperModal.value = true
}

const viewPaperDetails = (paper: any) => {
  selectedPaper.value = paper
  showDetailsModal.value = true
}

const deletePaper = async (paper: any) => {
  const gradeCount = getGradeCount(paper.id)
  const boundaryCount = getBoundaryCount(paper.id)

  let message = `Are you sure you want to delete "${paper.subjectName} - ${paper.paperId}"?`
  if (gradeCount > 0 || boundaryCount > 0) {
    message += `\n\nThis will also delete:\n• ${gradeCount} grade record(s)\n• ${boundaryCount} grade boundary(ies)`
  }
  message += '\n\nThis action cannot be undone.'

  const result = await sweetAlert.confirm(
    'Delete Paper',
    message,
    'warning'
  )

  if (result.isConfirmed && paper.id) {
    try {
      // Note: This would need to be implemented in the external API
      console.log('Delete paper:', paper.id)
      await sweetAlert.toast.success('Paper deleted successfully')
    } catch (error) {
      await sweetAlert.error('Error', 'Failed to delete paper')
    }
  }
}

const closePaperModal = () => {
  showPaperModal.value = false
  selectedPaper.value = null
  preSelectedSubject.value = ''
}

const closeDetailsModal = () => {
  showDetailsModal.value = false
  selectedPaper.value = null
}

const handlePaperSuccess = () => {
  closePaperModal()
  // Refresh papers list
  gradingStore.fetchPapers()
}

// Lifecycle
onMounted(async () => {
  // PaperManagement: Component mounted (console logging removed)

  // Fetch papers from external API
  try {
    // Fetching papers (console logging removed)
    await gradingApiStore.fetchPapers()
    // Papers loaded (console logging removed)

    // Fetching grade boundaries (console logging removed)
    await gradingApiStore.fetchGradeBoundaries()
    // Grade boundaries loaded (console logging removed)

    // Unique subjects extracted (console logging removed)
  } catch (error) {
    // Failed to fetch data (console logging removed)
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.hover\:text-maneb-primary:hover {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>
