<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-6">
    <!-- Breadcrumbs -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        <li class="inline-flex items-center">
          <router-link
            to="/admin/dashboard"
            class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-maneb-primary transition-colors duration-200"
          >
            <svg class="w-5 h-5 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
            </svg>
            Admin Dashboard
          </router-link>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <router-link
              to="/admin/user-management/users"
              class="ml-1 text-sm font-medium text-gray-700 hover:text-maneb-primary md:ml-2 transition-colors duration-200"
            >
              User Management
            </router-link>
          </div>
        </li>
        <li aria-current="page">
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="ml-1 text-sm font-medium text-gray-400 md:ml-2">Roles</span>
          </div>
        </li>
      </ol>
    </nav>

    <!-- Header -->
    <div class="mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Role Management</h1>
          <p class="mt-1 text-sm text-gray-600">Manage user roles and permissions</p>
        </div>
        <router-link
          :to="{ name: 'admin.user-management.roles.create' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add New Role
        </router-link>
      </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="bg-white shadow-sm border border-gray-200 rounded-lg mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex space-x-4">
            <h3 class="text-lg font-medium text-black">Roles Management</h3>
            <div class="flex space-x-2">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-maneb-primary text-white">
                Roles
              </span>
              <router-link
                :to="{ name: 'admin.user-management.roles.permissions' }"
                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors duration-200"
              >
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                </svg>
                Permissions
              </router-link>
            </div>
          </div>
        </div>
      </div>

      <!-- Table -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Role Name
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Permissions
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created Date
              </th>
              <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-if="isLoading">
              <td colspan="5" class="px-6 py-12 text-center">
                <div class="flex justify-center items-center">
                  <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-maneb-primary"></div>
                  <span class="ml-2 text-gray-500">Loading roles...</span>
                </div>
              </td>
            </tr>
            <tr v-else-if="filteredRoles.length === 0">
              <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                No roles found
              </td>
            </tr>
            <tr v-else v-for="role in filteredRoles" :key="role.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
                      <svg class="h-5 w-5 text-maneb-primary" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ role.name }}</div>
                    <div class="text-sm text-gray-500">{{ role.description || 'No description' }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusBadgeClass(role.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ role.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ getPermissionCount(role) }} permissions
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(role.createdAt) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <router-link
                    :to="{ name: 'admin.user-management.roles.detail', params: { id: role.id } }"
                    class="text-indigo-600 hover:text-indigo-900 p-2 rounded-md hover:bg-indigo-50 transition-colors duration-200"
                    title="View Role"
                  >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </router-link>
                  <router-link
                    :to="{ name: 'admin.user-management.roles.edit', params: { id: role.id } }"
                    class="text-maneb-primary hover:text-red-700 p-2 rounded-md hover:bg-red-50 transition-colors duration-200"
                    title="Edit Role"
                  >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </router-link>
                  <button
                    @click="deleteRole(role)"
                    class="text-red-600 hover:text-red-900 p-2 rounded-md hover:bg-red-50 transition-colors duration-200"
                    title="Delete Role"
                  >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useRoleStore, useAuthStore } from '@/store'
import { showSuccessAlert, showErrorAlert, showConfirmDialog } from '@/utils/sweetAlert'
import type { RoleDto, RecordStatus } from '@/interfaces'

// Router
const router = useRouter()

// Stores
const roleStore = useRoleStore()
const authStore = useAuthStore()

// Reactive data
const isLoading = ref(false)
const searchQuery = ref('')
const statusFilter = ref<RecordStatus | 'All'>('All')

// Computed
const filteredRoles = computed(() => {
  let roles = roleStore.roles || []

  if (searchQuery.value) {
    roles = roles.filter(role =>
      role.name?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      role.description?.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  if (statusFilter.value !== 'All') {
    roles = roles.filter(role => role.status === statusFilter.value)
  }

  return roles
})

// Methods
const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'Approved':
    case 'SecondApproved':
      return 'bg-green-100 text-green-800'
    case 'Unapproved':
      return 'bg-yellow-100 text-yellow-800'
    case 'Rejected':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getPermissionCount = (role: RoleDto) => {
  // Check both rolePermissions (API standard) and permissions (legacy) for backward compatibility
  return role.rolePermissions?.length || (role as any).permissions?.length || 0
}

const formatDate = (date: string | Date | undefined) => {
  if (!date) return 'N/A'
  return new Date(date).toLocaleDateString()
}

const deleteRole = async (role: RoleDto) => {
  try {
    const confirmed = await showConfirmDialog(
      'Delete Role',
      `Are you sure you want to delete "${role.name}"? This action cannot be undone.`,
      'warning'
    )

    if (confirmed && role.id) {
      await roleStore.deleteRole(role.id)
      await showSuccessAlert('Role deleted successfully!')
    }
  } catch (err) {
    await showErrorAlert('Failed to delete role. Please try again.')
  }
}

// Lifecycle
onMounted(async () => {
  isLoading.value = true
  try {
    console.log('Loading roles...')
    console.log('Auth token available:', !!authStore.token)
    console.log('User authenticated:', authStore.isAuthenticated)

    // Fetch roles and role permissions in parallel
    await Promise.all([
      roleStore.fetchRoles(),
      roleStore.fetchRolePermissions()
    ])

    // Map permissions to roles
    const allPermissions = roleStore.rolePermissions
    console.log('All role permissions:', allPermissions)

    // Update each role with its permission count
    roleStore.roles.forEach(role => {
      const rolePermissions = allPermissions.filter(permission => permission.roleId === role.id)
      // Add permissions to role object for display
      ;(role as any).rolePermissions = rolePermissions
      console.log(`Role ${role.name} has ${rolePermissions.length} permissions`)
    })

    console.log('Roles loaded successfully')
    console.log('Roles count:', roleStore.roles.length)
  } catch (error: any) {
    console.error('Error loading roles:', error)
    await showErrorAlert('Failed to load roles. Please try again.')
  } finally {
    isLoading.value = false
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.hover\:bg-red-700:hover {
  background-color: #b91c1c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}
</style>
