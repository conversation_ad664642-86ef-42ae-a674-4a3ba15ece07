<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- Breadcrumbs -->
    <BreadcrumbsActions :breadcrumbs="permissionsBreadcrumbs" />

    <!-- Header -->
    <div class="mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Permissions Management</h1>
          <p class="mt-1 text-sm text-gray-600">Manage system permissions and access controls</p>
        </div>
        <router-link
          :to="{ name: 'admin.user-management.permissions.create' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add New Permission
        </router-link>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-6">
      <div class="px-6 py-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Search -->
          <div>
            <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
            <input
              id="search"
              v-model="searchQuery"
              type="text"
              placeholder="Search permissions..."
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
            />
          </div>

          <!-- Section Filter -->
          <div>
            <label for="sectionFilter" class="block text-sm font-medium text-gray-700">Section</label>
            <select
              id="sectionFilter"
              v-model="selectedSection"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
            >
              <option value="All">All Sections</option>
              <option v-for="section in availableSections" :key="section.id" :value="section.id">
                {{ section.name }}
              </option>
            </select>
          </div>

          <!-- Action Filter -->
          <div>
            <label for="actionFilter" class="block text-sm font-medium text-gray-700">Action</label>
            <select
              id="actionFilter"
              v-model="selectedAction"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
            >
              <option value="All">All Actions</option>
              <option value="Create">Create</option>
              <option value="Read">Read</option>
              <option value="Update">Update</option>
              <option value="Delete">Delete</option>
              <option value="Approve">Approve</option>
              <option value="Export">Export</option>
            </select>
          </div>

          <!-- Status Filter -->
          <div>
            <label for="statusFilter" class="block text-sm font-medium text-gray-700">Status</label>
            <select
              id="statusFilter"
              v-model="selectedStatus"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
            >
              <option value="All">All Statuses</option>
              <option value="Approved">Approved</option>
              <option value="SecondApproved">Second Approved</option>
              <option value="Unapproved">Unapproved</option>
              <option value="Rejected">Rejected</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-maneb-primary"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error loading permissions</h3>
          <div class="mt-2 text-sm text-red-700">{{ error }}</div>
        </div>
      </div>
    </div>

    <!-- Permissions Table -->
    <div v-else class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">
          Permissions ({{ filteredPermissions.length }})
        </h3>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Permission
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Section
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Action
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="permission in paginatedPermissions" :key="permission.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="bg-red-50 p-2 rounded-full mr-3">
                    <svg class="h-4 w-4 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ getPermissionDisplayName(permission) }}</div>
                    <div class="text-sm text-gray-500">{{ permission.action }} access to {{ getSectionName(permission.sectionID) }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ getSectionName(permission.sectionID) }}</div>
                <div v-if="getSectionController(permission.sectionID)" class="text-sm text-gray-500">{{ getSectionController(permission.sectionID) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getActionBadgeClass(permission.action)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ permission.action }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusBadgeClass(permission.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ permission.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(permission.dateCreated) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center space-x-2">
                  <!-- View Details Button -->
                  <button
                    @click="viewPermission(permission)"
                    type="button"
                    title="View Details"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-center text-blue-600 bg-blue-100 rounded-lg hover:bg-blue-200 focus:ring-4 focus:ring-blue-300"
                  >
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>

                  <!-- Edit Button -->
                  <button
                    @click="editPermission(permission)"
                    type="button"
                    title="Edit Permission"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-center text-white bg-maneb-primary rounded-lg hover:bg-maneb-primary-dark focus:ring-4 focus:ring-red-300"
                  >
                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"></path>
                      <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path>
                    </svg>
                  </button>

                  <!-- Status Management Button -->
                  <button
                    @click="managePermissionStatus(permission)"
                    type="button"
                    title="Manage Status"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-center text-green-600 bg-green-100 rounded-lg hover:bg-green-200 focus:ring-4 focus:ring-green-300"
                  >
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </button>

                  <!-- Delete Button -->
                  <button
                    @click="deletePermission(permission)"
                    type="button"
                    title="Delete Permission"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-center text-red-600 bg-red-100 rounded-lg hover:bg-red-200 focus:ring-4 focus:ring-red-300"
                  >
                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty State for Table -->
      <div v-if="paginatedPermissions.length === 0" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No permissions match your filters</h3>
        <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria.</p>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!isLoading && !error && filteredPermissions.length === 0" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No permissions found</h3>
      <p class="mt-1 text-sm text-gray-500">Get started by creating a new permission.</p>
      <div class="mt-6">
        <router-link
          :to="{ name: 'admin.user-management.permissions.create' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Permission
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useRoleStore, useSectionStore } from '@/stores'
import BreadcrumbsActions from '@/components/UI/breadcrumbs/breadcrumbs_actions.vue'
import { showConfirmDialog, showSuccessAlert } from '@/utils/ui/sweetAlert'
import type { RolePermissionDto, SectionDto, PermissionAction, RecordStatus } from '@/interfaces'

// Router
const router = useRouter()

// Stores
const roleStore = useRoleStore()
const sectionStore = useSectionStore()

// Reactive state
const permissions = ref<RolePermissionDto[]>([])
const availableSections = ref<SectionDto[]>([])
const isLoading = ref(false)
const error = ref<string | null>(null)

// Filters
const searchQuery = ref('')
const selectedSection = ref<string>('All')
const selectedAction = ref<string>('All')
const selectedStatus = ref<string>('All')

// Pagination
const currentPage = ref(1)
const itemsPerPage = ref(10)

// Computed
const permissionsBreadcrumbs = [
  { name: 'Admin', href: '/admin', current: false },
  { name: 'User Management', href: '/admin/user-management', current: false },
  { name: 'Permissions', href: '/admin/user-management/permissions', current: true },
]

const filteredPermissions = computed(() => {
  let filtered = permissions.value

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(permission =>
      permission.action?.toLowerCase().includes(query) ||
      permission.sectionID?.toLowerCase().includes(query) ||
      getSectionName(permission.sectionID)?.toLowerCase().includes(query) ||
      getPermissionDisplayName(permission)?.toLowerCase().includes(query)
    )
  }

  // Section filter
  if (selectedSection.value !== 'All') {
    filtered = filtered.filter(permission => permission.sectionID === selectedSection.value)
  }

  // Action filter
  if (selectedAction.value !== 'All') {
    filtered = filtered.filter(permission => permission.action === selectedAction.value)
  }

  // Status filter
  if (selectedStatus.value !== 'All') {
    filtered = filtered.filter(permission => permission.status === selectedStatus.value)
  }

  return filtered
})

const totalPages = computed(() => Math.ceil(filteredPermissions.value.length / itemsPerPage.value))

const startIndex = computed(() => (currentPage.value - 1) * itemsPerPage.value)
const endIndex = computed(() => startIndex.value + itemsPerPage.value)

const paginatedPermissions = computed(() =>
  filteredPermissions.value.slice(startIndex.value, endIndex.value)
)

const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, currentPage.value + 2)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
})

// Methods
const getActionBadgeClass = (action?: PermissionAction) => {
  switch (action) {
    case 'Create': return 'bg-green-100 text-green-800'
    case 'View': return 'bg-blue-100 text-blue-800'
    case 'ViewAll': return 'bg-blue-100 text-blue-800'
    case 'Update': return 'bg-yellow-100 text-yellow-800'
    case 'Delete': return 'bg-red-100 text-red-800'
    case 'Approve': return 'bg-purple-100 text-purple-800'
    case 'SecondApprove': return 'bg-purple-100 text-purple-800'
    case 'Authorise': return 'bg-indigo-100 text-indigo-800'
    case 'Login': return 'bg-green-100 text-green-800'
    case 'Logout': return 'bg-gray-100 text-gray-800'
    case 'Reject': return 'bg-red-100 text-red-800'
    case 'All': return 'bg-purple-100 text-purple-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getStatusBadgeClass = (status?: RecordStatus) => {
  switch (status) {
    case 'Approved': return 'bg-green-100 text-green-800'
    case 'SecondApproved': return 'bg-blue-100 text-blue-800'
    case 'Unapproved': return 'bg-yellow-100 text-yellow-800'
    case 'Rejected': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const formatDate = (date?: Date | string): string => {
  if (!date) return 'N/A'

  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getSectionName = (sectionID: string): string => {
  const section = availableSections.value.find(s => s.id === sectionID)
  return section?.name || sectionID || 'Unknown Section'
}

const getSectionController = (sectionID: string): string | undefined => {
  const section = availableSections.value.find(s => s.id === sectionID)
  return section?.controller
}

const getPermissionDisplayName = (permission: RolePermissionDto): string => {
  const sectionName = getSectionName(permission.sectionID)
  return `${permission.action} - ${sectionName}`
}

const viewPermission = (permission: RolePermissionDto) => {
  if (permission.id) {
    router.push({ name: 'admin.user-management.permissions.detail', params: { id: permission.id } })
  }
}

const editPermission = (permission: RolePermissionDto) => {
  if (permission.id) {
    router.push({ name: 'admin.user-management.permissions.edit', params: { id: permission.id } })
  }
}

const managePermissionStatus = (permission: RolePermissionDto) => {
  if (permission.id) {
    router.push({ name: 'admin.user-management.permissions.status', params: { id: permission.id } })
  }
}

const deletePermission = async (permission: RolePermissionDto) => {
  if (!permission.id) return

  const confirmed = await showConfirmDialog(
    'Delete Permission',
    `Are you sure you want to delete the permission "${permission.action} - ${permission.sectionID}"? This action cannot be undone.`,
    'Yes, Delete Permission'
  )

  if (!confirmed) return

  try {
    await roleStore.deleteRolePermission(permission.id)
    await fetchPermissions()
    await showSuccessAlert('Permission deleted successfully!')
  } catch (error: any) {
    console.error('Error deleting permission:', error)
  }
}

const fetchPermissions = async () => {
  try {
    isLoading.value = true
    error.value = null
    await roleStore.fetchRolePermissions()
    permissions.value = roleStore.rolePermissions

    // If no permissions from API, add sample permissions for development
    if (permissions.value.length === 0) {
      permissions.value = [
        {
          id: '1',
          sectionID: 'ROLE_PERMISSION',
          roleId: 'admin-role',
          action: 'All',
          canAccess: true,
          status: 'Approved',
          dateCreated: new Date('2025-07-11')
        },
        {
          id: '2',
          sectionID: 'SECTION',
          roleId: 'admin-role',
          action: 'All',
          canAccess: true,
          status: 'Approved',
          dateCreated: new Date('2025-07-11')
        },
        {
          id: '3',
          sectionID: 'USER',
          roleId: 'admin-role',
          action: 'All',
          canAccess: true,
          status: 'Approved',
          dateCreated: new Date('2025-07-11')
        },
        {
          id: '4',
          sectionID: 'ROLE',
          roleId: 'admin-role',
          action: 'All',
          canAccess: true,
          status: 'Approved',
          dateCreated: new Date('2025-07-11')
        }
      ]
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to fetch permissions'
    console.error('Error fetching permissions:', err)
    // Fallback to sample permissions if API fails
    permissions.value = [
      {
        id: '1',
        sectionID: 'ROLE_PERMISSION',
        roleId: 'admin-role',
        action: 'All',
        canAccess: true,
        status: 'Approved',
        dateCreated: new Date('2025-07-11')
      },
      {
        id: '2',
        sectionID: 'SECTION',
        roleId: 'admin-role',
        action: 'All',
        canAccess: true,
        status: 'Approved',
        dateCreated: new Date('2025-07-11')
      },
      {
        id: '3',
        sectionID: 'USER',
        roleId: 'admin-role',
        action: 'All',
        canAccess: true,
        status: 'Approved',
        dateCreated: new Date('2025-07-11')
      },
      {
        id: '4',
        sectionID: 'ROLE',
        roleId: 'admin-role',
        action: 'All',
        canAccess: true,
        status: 'Approved',
        dateCreated: new Date('2025-07-11')
      }
    ]
  } finally {
    isLoading.value = false
  }
}

const fetchSections = async () => {
  try {
    await sectionStore.fetchSections()
    availableSections.value = sectionStore.sections.filter(section =>
      section.status === 'Approved' || section.status === 'SecondApproved'
    )

    // If no sections from API, add sample sections for development
    if (availableSections.value.length === 0) {
      availableSections.value = [
        { id: 'ROLE_PERMISSION', name: 'Role Permission', controller: 'RolePermission', status: 'Approved' },
        { id: 'SECTION', name: 'Section Management', controller: 'Section', status: 'Approved' },
        { id: 'USER', name: 'User Management', controller: 'User', status: 'Approved' },
        { id: 'ROLE', name: 'Role Management', controller: 'Role', status: 'Approved' }
      ]
    }
  } catch (error) {
    console.error('Error fetching sections:', error)
    // Fallback to sample sections if API fails
    availableSections.value = [
      { id: 'ROLE_PERMISSION', name: 'Role Permission', controller: 'RolePermission', status: 'Approved' },
      { id: 'SECTION', name: 'Section Management', controller: 'Section', status: 'Approved' },
      { id: 'USER', name: 'User Management', controller: 'User', status: 'Approved' },
      { id: 'ROLE', name: 'Role Management', controller: 'Role', status: 'Approved' }
    ]
  }
}

// Pagination methods
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

// Watch for filter changes to reset pagination
watch([searchQuery, selectedSection, selectedAction, selectedStatus], () => {
  currentPage.value = 1
})

// Lifecycle
onMounted(async () => {
  await Promise.all([
    fetchPermissions(),
    fetchSections()
  ])
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary-dark:hover {
  background-color: #8b2424;
}
</style>
