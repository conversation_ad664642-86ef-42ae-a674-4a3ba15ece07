<template>
  <BaseModal
    :is-open="isOpen"
    :title="logType === 'grade' ? 'Grade Audit Logs' : 'System Audit Logs'"
    :description="`View detailed audit trail of ${logType === 'grade' ? 'grading' : 'system'} activities`"
    size="6xl"
    modal-id="audit-log-modal"
    show-icon
    variant="default"
    @close="closeModal"
  >
    <template #icon>
      <svg class="w-6 h-6 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
    </template>

    <template #body>
      <div class="space-y-6 max-h-96 overflow-y-auto">
          <!-- Loading State -->
          <div v-if="isLoading" class="flex justify-center items-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-maneb-primary"></div>
            <span class="ml-3 text-gray-600">Loading audit logs...</span>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Error Loading Audit Logs</h3>
                <div class="mt-2 text-sm text-red-700">{{ error }}</div>
                <div class="mt-3">
                  <button
                    @click="loadAuditLogs"
                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    Retry
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Audit Logs Table -->
          <div v-else-if="auditLogs.length > 0" class="space-y-4">
            <!-- Filters -->
            <div class="flex flex-wrap gap-4 p-4 bg-gray-50 rounded-lg">
              <div class="flex-1 min-w-64">
                <label for="actionFilter" class="block text-sm font-medium text-gray-700 mb-1">Filter by Action</label>
                <select
                  id="actionFilter"
                  v-model="actionFilter"
                  class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2"
                >
                  <option value="">All Actions</option>
                  <option v-for="action in uniqueActions" :key="action" :value="action">{{ action }}</option>
                </select>
              </div>
              <div class="flex-1 min-w-64">
                <label for="entityFilter" class="block text-sm font-medium text-gray-700 mb-1">Filter by Entity</label>
                <select
                  id="entityFilter"
                  v-model="entityFilter"
                  class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2"
                >
                  <option value="">All Entities</option>
                  <option v-for="entity in uniqueEntities" :key="entity" :value="entity">{{ entity }}</option>
                </select>
              </div>
              <div class="flex items-end">
                <button
                  @click="clearFilters"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
                >
                  Clear Filters
                </button>
              </div>
            </div>

            <!-- Table -->
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Timestamp
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Action
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Entity
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Performed By
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Details
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="log in filteredLogs" :key="log.logId" class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ formatDate(log.performedAt) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="getActionBadgeClass(log.action)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                        {{ log.action || 'Unknown' }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>
                        <div class="font-medium">{{ log.entity || 'N/A' }}</div>
                        <div v-if="log.entityId" class="text-gray-500">ID: {{ log.entityId }}</div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ log.performedBy || 'System' }}
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-900 max-w-xs">
                      <div class="truncate" :title="log.details">
                        {{ log.details || 'No details available' }}
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Pagination Info -->
            <div class="flex items-center justify-between px-4 py-3 bg-gray-50 border-t border-gray-200 rounded-b-lg">
              <div class="flex-1 flex justify-between sm:hidden">
                <span class="text-sm text-gray-700">
                  Showing {{ filteredLogs.length }} of {{ auditLogs.length }} entries
                </span>
              </div>
              <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p class="text-sm text-gray-700">
                    Showing <span class="font-medium">{{ filteredLogs.length }}</span> of 
                    <span class="font-medium">{{ auditLogs.length }}</span> entries
                  </p>
                </div>
                <div>
                  <button
                    @click="loadAuditLogs"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
                  >
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Refresh
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No audit logs found</h3>
            <p class="mt-1 text-sm text-gray-500">No audit trail entries are available at this time.</p>
            <div class="mt-6">
              <button
                @click="loadAuditLogs"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
              >
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Load Audit Logs
              </button>
            </div>
          </div>
        </div>
    </template>

    <template #footer>
      <button
        @click="closeModal"
        class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
      >
        Close
      </button>
    </template>
  </BaseModal>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import BaseModal from './BaseModal.vue'
import { useGradingApiStore } from '@/store'
import type { AuditLog } from '@/interfaces'

// Props
interface Props {
  isOpen: boolean
  logType?: 'system' | 'grade'
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  logType: 'system'
})

// Emits
const emit = defineEmits<{
  close: []
}>()

// Store
const gradingApiStore = useGradingApiStore()

// Reactive state
const actionFilter = ref('')
const entityFilter = ref('')
const error = ref<string | null>(null)

// Computed
const isLoading = computed(() => gradingApiStore.isLoading)
const auditLogs = computed(() => 
  props.logType === 'grade' 
    ? gradingApiStore.gradeAuditLogs 
    : gradingApiStore.auditLogs
)

const uniqueActions = computed(() => {
  const actions = auditLogs.value
    .map(log => log.action)
    .filter(action => action)
    .filter((action, index, arr) => arr.indexOf(action) === index)
  return actions.sort()
})

const uniqueEntities = computed(() => {
  const entities = auditLogs.value
    .map(log => log.entity)
    .filter(entity => entity)
    .filter((entity, index, arr) => arr.indexOf(entity) === index)
  return entities.sort()
})

const filteredLogs = computed(() => {
  let filtered = auditLogs.value

  if (actionFilter.value) {
    filtered = filtered.filter(log => log.action === actionFilter.value)
  }

  if (entityFilter.value) {
    filtered = filtered.filter(log => log.entity === entityFilter.value)
  }

  // Sort by timestamp descending (newest first)
  return filtered.sort((a, b) => {
    const dateA = new Date(a.performedAt || 0).getTime()
    const dateB = new Date(b.performedAt || 0).getTime()
    return dateB - dateA
  })
})

// Methods
const loadAuditLogs = async () => {
  try {
    error.value = null
    if (props.logType === 'grade') {
      await gradingApiStore.fetchGradeAuditLogs()
    } else {
      await gradingApiStore.fetchAuditLogs()
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to load audit logs'
  }
}

const clearFilters = () => {
  actionFilter.value = ''
  entityFilter.value = ''
}

const formatDate = (date?: Date | string): string => {
  if (!date) return 'N/A'
  return new Date(date).toLocaleString()
}

const getActionBadgeClass = (action?: string): string => {
  if (!action) return 'bg-gray-100 text-gray-800'
  
  const actionLower = action.toLowerCase()
  if (actionLower.includes('create') || actionLower.includes('add')) {
    return 'bg-green-100 text-green-800'
  } else if (actionLower.includes('update') || actionLower.includes('edit')) {
    return 'bg-blue-100 text-blue-800'
  } else if (actionLower.includes('delete') || actionLower.includes('remove')) {
    return 'bg-red-100 text-red-800'
  } else if (actionLower.includes('approve') || actionLower.includes('verify')) {
    return 'bg-purple-100 text-purple-800'
  } else {
    return 'bg-gray-100 text-gray-800'
  }
}

const closeModal = () => {
  emit('close')
  clearFilters()
}

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    document.body.style.overflow = 'hidden'
    loadAuditLogs()
  } else {
    document.body.style.overflow = 'auto'
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary-dark:hover {
  background-color: #8b2424;
}
</style>
