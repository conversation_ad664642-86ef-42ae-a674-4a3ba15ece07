<template>
  <div class="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8 py-6">
    <!-- Enhanced <PERSON>er with MANEB Branding -->
    <div class="mb-8">
      <nav class="flex mb-5" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 text-sm font-medium md:space-x-2">
          <li class="inline-flex items-center">
            <router-link
              to="/admin/dashboard"
              class="inline-flex items-center text-gray-700 hover:text-maneb-primary dark:text-gray-300 dark:hover:text-white transition-colors duration-200"
            >
              <svg class="w-5 h-5 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
              </svg>
              Admin Dashboard
            </router-link>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <router-link
                to="/admin/user-management/users"
                class="ml-1 text-gray-700 hover:text-maneb-primary md:ml-2 dark:text-gray-300 dark:hover:text-white transition-colors duration-200"
              >
                User Management
              </router-link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="ml-1 text-gray-400 md:ml-2 dark:text-gray-500" aria-current="page">User Details</span>
            </div>
          </li>
        </ol>
      </nav>
    </div>

    <!-- Enhanced Loading State -->
    <div v-if="isLoading" class="flex flex-col justify-center items-center py-16">
      <div class="animate-spin rounded-full h-16 w-16 border-4 border-gray-200 border-t-maneb-primary"></div>
      <p class="mt-4 text-sm text-gray-600 dark:text-gray-400">Loading user details...</p>
    </div>

    <!-- Enhanced Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6 dark:bg-red-900/20 dark:border-red-800">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-6 w-6 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3 flex-1">
          <h3 class="text-sm font-medium text-red-800 dark:text-red-400">Error loading user details</h3>
          <div class="mt-2 text-sm text-red-700 dark:text-red-300">{{ error }}</div>
          <div class="mt-4">
            <button
              @click="fetchUserDetails"
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced User Detail Content -->
    <div v-else-if="user" class="space-y-8">
      <!-- Enhanced Header Section -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-6">
          <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <!-- User Info -->
            <div class="flex items-center space-x-6">
              <!-- Avatar -->
              <div class="flex-shrink-0">
                <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center">
                  <span class="text-2xl font-bold text-maneb-primary">
                    {{ getUserInitials(user) }}
                  </span>
                </div>
              </div>

              <!-- User Details -->
              <div class="flex-1 min-w-0">
                <h1 class="text-3xl font-bold text-gray-900 truncate">
                  {{ user.fullName || `${user.firstName} ${user.lastName}` }}
                </h1>
                <div class="mt-2 flex flex-col sm:flex-row sm:flex-wrap sm:space-x-6">
                  <div class="flex items-center text-sm text-gray-500">
                    <svg class="flex-shrink-0 mr-1.5 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                    </svg>
                    {{ user.email || 'No email' }}
                  </div>
                  <div v-if="user.userName" class="flex items-center text-sm text-gray-500">
                    <svg class="flex-shrink-0 mr-1.5 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                    </svg>
                    @{{ user.userName }}
                  </div>
                  <div v-if="user.idNumber" class="flex items-center text-sm text-gray-500">
                    <svg class="flex-shrink-0 mr-1.5 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z" clip-rule="evenodd"/>
                    </svg>
                    ID: {{ user.idNumber }}
                  </div>
                </div>

                <!-- Status and Role -->
                <div class="mt-3 flex flex-wrap items-center gap-3">
                  <span :class="getStatusBadgeClass(user.status)" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium">
                    {{ getStatusDisplayText(user.status) }}
                  </span>
                  <template v-if="getUserRoles(user).length > 0">
                    <span v-for="role in getUserRoles(user)" :key="role.id" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                      {{ role.name }}
                    </span>
                  </template>
                  <span v-else class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                    No Roles Assigned
                  </span>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <router-link
                :to="{ name: 'admin.user-management.users.edit', params: { id: user.id } }"
                class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary transition-colors duration-200"
              >
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit User
              </router-link>

              <router-link
                :to="{ name: 'admin.user-management.users.status', params: { id: user.id } }"
                class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary transition-colors duration-200 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600"
              >
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Manage Status
              </router-link>

              <router-link
                :to="{ name: 'admin.user-management.users.roles', params: { id: user.id } }"
                class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary transition-colors duration-200 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600"
              >
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
                Manage Roles
              </router-link>
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Information Cards -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Personal Information -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-gray-900">Personal Information</h3>
            </div>
          </div>
          <div class="px-6 py-6 space-y-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div class="space-y-1">
                <label class="text-sm font-medium text-gray-500">First Name</label>
                <p class="text-sm text-gray-900 font-medium">{{ user.firstName || 'N/A' }}</p>
              </div>
              <div class="space-y-1">
                <label class="text-sm font-medium text-gray-500">Last Name</label>
                <p class="text-sm text-gray-900 font-medium">{{ user.lastName || 'N/A' }}</p>
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div class="space-y-1">
                <label class="text-sm font-medium text-gray-500">Gender</label>
                <p class="text-sm text-gray-900 font-medium">{{ user.gender || 'Not specified' }}</p>
              </div>
              <div class="space-y-1">
                <label class="text-sm font-medium text-gray-500">Date of Birth</label>
                <p class="text-sm text-gray-900 font-medium">{{ formatDate(user.dateOfBirth) }}</p>
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div class="space-y-1">
                <label class="text-sm font-medium text-gray-500">ID Type</label>
                <p class="text-sm text-gray-900 font-medium">{{ user.idType || 'Not specified' }}</p>
              </div>
              <div class="space-y-1">
                <label class="text-sm font-medium text-gray-500">ID Number</label>
                <p class="text-sm text-gray-900 font-medium">{{ user.idNumber || 'Not specified' }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Account Information -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Account Information</h3>
          </div>
          <div class="px-6 py-4 space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Email Address</label>
              <p class="mt-1 text-sm text-gray-900">{{ user.email || 'N/A' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Username</label>
              <p class="mt-1 text-sm text-gray-900">{{ user.userName || 'N/A' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Account Status</label>
              <div class="mt-1">
                <span :class="getStatusBadgeClass(user.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ user.status }}
                </span>
              </div>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Roles</label>
              <div class="mt-1">
                <template v-if="getUserRoles(user).length > 0">
                  <div class="flex flex-wrap gap-2">
                    <span v-for="role in getUserRoles(user)" :key="role.id" class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                      {{ role.name }}
                    </span>
                  </div>
                </template>
                <p v-else class="text-sm text-gray-900">No roles assigned</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- System Information -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">System Information</h3>
        </div>
        <div class="px-6 py-4">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="text-sm font-medium text-gray-500">User ID</label>
              <p class="mt-1 text-sm text-gray-900 font-mono">{{ user.id || 'N/A' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Created Date</label>
              <p class="mt-1 text-sm text-gray-900">{{ formatDate(user.dateCreated) }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Created By</label>
              <p class="mt-1 text-sm text-gray-900">{{ user.createdBy || 'System' }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-between items-center">
        <router-link 
          :to="{ name: 'admin.user-management.users' }"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to Users
        </router-link>
        
        <div class="flex space-x-3">
          <button
            @click="navigateToStatusEdit"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Manage Status
          </button>

          <button
            @click="navigateToRoleAssignment"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
            Assign Roles
          </button>
        </div>
      </div>
    </div>

    <!-- User Not Found -->
    <div v-else class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">User not found</h3>
      <p class="mt-1 text-sm text-gray-500">The requested user could not be found.</p>
      <div class="mt-6">
        <router-link 
          :to="{ name: 'admin.user-management.users' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
        >
          Back to Users
        </router-link>
      </div>
    </div>

    <!-- Using page-based forms instead of modals -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/store'
import type { UserDto, RecordStatus } from '@/interfaces'
// Using page-based forms instead of modals

// Router
const route = useRoute()
const router = useRouter()

// Store
const userStore = useUserStore()

// Reactive state
const user = ref<UserDto | null>(null)
const isLoading = ref(false)
const error = ref<string | null>(null)

// Using page-based forms instead of modal state

// Computed
const userId = computed(() => route.params.id as string)

const userDetailBreadcrumbs = computed(() => [
  { name: 'Admin', href: '/admin', current: false },
  { name: 'User Management', href: '/admin/user-management', current: false },
  { name: 'Users', href: '/admin/user-management/users', current: false },
  { name: user.value?.fullName || `${user.value?.firstName} ${user.value?.lastName}` || 'User Details', href: '', current: true },
])

// Methods
const getStatusBadgeClass = (status?: RecordStatus) => {
  switch (status) {
    case 'Approved':
      return 'bg-green-100 text-green-800'
    case 'SecondApproved':
      return 'bg-blue-100 text-blue-800'
    case 'Unapproved':
      return 'bg-yellow-100 text-yellow-800'
    case 'Rejected':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const formatDate = (date?: Date | string): string => {
  if (!date) return 'N/A'

  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const getUserInitials = (user: UserDto): string => {
  const first = user.firstName?.charAt(0) || ''
  const last = user.lastName?.charAt(0) || ''
  return (first + last).toUpperCase()
}

const getStatusDisplayText = (status?: RecordStatus): string => {
  switch (status) {
    case 'Approved':
      return 'Approved'
    case 'SecondApproved':
      return 'Second Approved'
    case 'Unapproved':
      return 'Pending Approval'
    case 'Rejected':
      return 'Rejected'
    default:
      return 'Unknown'
  }
}

// Helper function to get user roles (handles both new single role and legacy roles array)
const getUserRoles = (user: UserDto) => {
  if (user.roles && user.roles.length > 0) {
    // Legacy format: roles array
    return user.roles
  } else if (user.role) {
    // New format: single role object
    return [user.role]
  }
  return []
}

const fetchUserDetails = async () => {
  if (!userId.value) {
    error.value = 'No user ID provided'
    return
  }

  try {
    isLoading.value = true
    error.value = null
    user.value = await userStore.fetchUserById(userId.value)
  } catch (err: any) {
    error.value = err.message || 'Failed to load user details'
    console.error('Error fetching user details:', err)
  } finally {
    isLoading.value = false
  }
}

// Page-based navigation methods
const navigateToStatusEdit = () => {
  router.push({
    name: 'admin.user-management.users.edit-status',
    params: { id: userId.value }
  })
}

const navigateToRoleAssignment = () => {
  router.push({
    name: 'admin.user-management.users.assign-role',
    params: { id: userId.value }
  })
}

// Lifecycle
onMounted(() => {
  fetchUserDetails()
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary-dark:hover {
  background-color: #8b2424;
}
</style>
