<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- Breadcrumbs -->
    <BreadcrumbsActions :breadcrumbs="sectionsBreadcrumbs" />

    <!-- Header -->
    <div class="mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Sections Management</h1>
          <p class="mt-1 text-sm text-gray-600">Manage system sections and organizational structure</p>
        </div>
        <router-link
          :to="{ name: 'admin.user-management.sections.create' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add New Section
        </router-link>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-6">
      <div class="px-6 py-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Search -->
          <div>
            <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
            <input
              id="search"
              v-model="searchQuery"
              type="text"
              placeholder="Search sections..."
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
            />
          </div>

          <!-- Controller Filter -->
          <div>
            <label for="controllerFilter" class="block text-sm font-medium text-gray-700">Controller</label>
            <select
              id="controllerFilter"
              v-model="selectedController"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
            >
              <option value="All">All Controllers</option>
              <option v-for="controller in availableControllers" :key="controller" :value="controller">
                {{ controller }}
              </option>
            </select>
          </div>

          <!-- Area Filter -->
          <div>
            <label for="areaFilter" class="block text-sm font-medium text-gray-700">Area</label>
            <select
              id="areaFilter"
              v-model="selectedArea"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
            >
              <option value="All">All Areas</option>
              <option v-for="area in availableAreas" :key="area" :value="area">
                {{ area }}
              </option>
            </select>
          </div>

          <!-- Status Filter -->
          <div>
            <label for="statusFilter" class="block text-sm font-medium text-gray-700">Status</label>
            <select
              id="statusFilter"
              v-model="selectedStatus"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
            >
              <option value="All">All Statuses</option>
              <option value="Approved">Approved</option>
              <option value="SecondApproved">Second Approved</option>
              <option value="Unapproved">Unapproved</option>
              <option value="Rejected">Rejected</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-maneb-primary"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error loading sections</h3>
          <div class="mt-2 text-sm text-red-700">{{ error }}</div>
        </div>
      </div>
    </div>

    <!-- Sections Table -->
    <div v-else class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">
          Sections ({{ filteredSections.length }})
        </h3>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Section
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Controller
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Area
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="section in paginatedSections" :key="section.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="bg-red-50 p-2 rounded-full mr-3">
                    <svg class="h-4 w-4 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                  </div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ section.name }}</div>
                    <div class="text-sm text-gray-500">ID: {{ section.id }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ section.controller || 'N/A' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ section.area || 'N/A' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusBadgeClass(section.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ section.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(section.dateCreated) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center space-x-2">
                  <!-- View Details Button -->
                  <button
                    @click="viewSection(section)"
                    type="button"
                    title="View Details"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-center text-blue-600 bg-blue-100 rounded-lg hover:bg-blue-200 focus:ring-4 focus:ring-blue-300"
                  >
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>

                  <!-- Edit Button -->
                  <button
                    @click="editSection(section)"
                    type="button"
                    title="Edit Section"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-center text-white bg-maneb-primary rounded-lg hover:bg-maneb-primary-dark focus:ring-4 focus:ring-red-300"
                  >
                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"></path>
                      <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path>
                    </svg>
                  </button>

                  <!-- Status Management Button -->
                  <button
                    @click="manageSectionStatus(section)"
                    type="button"
                    title="Manage Status"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-center text-green-600 bg-green-100 rounded-lg hover:bg-green-200 focus:ring-4 focus:ring-green-300"
                  >
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </button>

                  <!-- Delete Button -->
                  <button
                    @click="deleteSection(section)"
                    type="button"
                    title="Delete Section"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-center text-red-600 bg-red-100 rounded-lg hover:bg-red-200 focus:ring-4 focus:ring-red-300"
                  >
                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty State for Table -->
      <div v-if="paginatedSections.length === 0" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No sections match your filters</h3>
        <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria.</p>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!isLoading && !error && filteredSections.length === 0" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No sections found</h3>
      <p class="mt-1 text-sm text-gray-500">Get started by creating a new section.</p>
      <div class="mt-6">
        <router-link
          :to="{ name: 'admin.user-management.sections.create' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Section
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useSectionStore } from '@/stores'
import BreadcrumbsActions from '@/components/UI/breadcrumbs/breadcrumbs_actions.vue'
import { showConfirmDialog, showSuccessAlert } from '@/utils/ui/sweetAlert'
import type { SectionDto, RecordStatus } from '@/interfaces'

// Router
const router = useRouter()

// Store
const sectionStore = useSectionStore()

// Reactive state
const sections = ref<SectionDto[]>([])
const isLoading = ref(false)
const error = ref<string | null>(null)

// Filters
const searchQuery = ref('')
const selectedController = ref<string>('All')
const selectedArea = ref<string>('All')
const selectedStatus = ref<string>('All')

// Pagination
const currentPage = ref(1)
const itemsPerPage = ref(10)

// Computed
const sectionsBreadcrumbs = [
  { name: 'Admin', href: '/admin', current: false },
  { name: 'User Management', href: '/admin/user-management', current: false },
  { name: 'Sections', href: '/admin/user-management/sections', current: true },
]

const availableControllers = computed(() => {
  const controllers = new Set<string>()
  sections.value.forEach(section => {
    if (section.controller) {
      controllers.add(section.controller)
    }
  })
  return Array.from(controllers).sort()
})

const availableAreas = computed(() => {
  const areas = new Set<string>()
  sections.value.forEach(section => {
    if (section.area) {
      areas.add(section.area)
    }
  })
  return Array.from(areas).sort()
})

const filteredSections = computed(() => {
  let filtered = sections.value

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(section =>
      section.name?.toLowerCase().includes(query) ||
      section.controller?.toLowerCase().includes(query) ||
      section.area?.toLowerCase().includes(query)
    )
  }

  // Controller filter
  if (selectedController.value !== 'All') {
    filtered = filtered.filter(section => section.controller === selectedController.value)
  }

  // Area filter
  if (selectedArea.value !== 'All') {
    filtered = filtered.filter(section => section.area === selectedArea.value)
  }

  // Status filter
  if (selectedStatus.value !== 'All') {
    filtered = filtered.filter(section => section.status === selectedStatus.value)
  }

  return filtered
})

const totalPages = computed(() => Math.ceil(filteredSections.value.length / itemsPerPage.value))

const startIndex = computed(() => (currentPage.value - 1) * itemsPerPage.value)
const endIndex = computed(() => startIndex.value + itemsPerPage.value)

const paginatedSections = computed(() =>
  filteredSections.value.slice(startIndex.value, endIndex.value)
)

// Methods
const getStatusBadgeClass = (status?: RecordStatus) => {
  switch (status) {
    case 'Approved': return 'bg-green-100 text-green-800'
    case 'SecondApproved': return 'bg-blue-100 text-blue-800'
    case 'Unapproved': return 'bg-yellow-100 text-yellow-800'
    case 'Rejected': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const formatDate = (date?: Date | string): string => {
  if (!date) return 'N/A'

  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const viewSection = (section: SectionDto) => {
  if (section.id) {
    router.push({ name: 'admin.user-management.sections.detail', params: { id: section.id } })
  }
}

const editSection = (section: SectionDto) => {
  if (section.id) {
    router.push({ name: 'admin.user-management.sections.edit', params: { id: section.id } })
  }
}

const manageSectionStatus = (section: SectionDto) => {
  if (section.id) {
    router.push({ name: 'admin.user-management.sections.status', params: { id: section.id } })
  }
}

const deleteSection = async (section: SectionDto) => {
  if (!section.id) return

  const confirmed = await showConfirmDialog(
    'Delete Section',
    `Are you sure you want to delete the section "${section.name}"? This action cannot be undone.`,
    'Yes, Delete Section'
  )

  if (!confirmed) return

  try {
    await sectionStore.deleteSection(section.id)
    await fetchSections()
    await showSuccessAlert('Section deleted successfully!')
  } catch (error: any) {
    console.error('Error deleting section:', error)
  }
}

const fetchSections = async () => {
  try {
    isLoading.value = true
    error.value = null
    await sectionStore.fetchSections()
    sections.value = sectionStore.sections
  } catch (err: any) {
    error.value = err.message || 'Failed to fetch sections'
    console.error('Error fetching sections:', err)
  } finally {
    isLoading.value = false
  }
}

// Watch for filter changes to reset pagination
watch([searchQuery, selectedController, selectedArea, selectedStatus], () => {
  currentPage.value = 1
})

// Lifecycle
onMounted(() => {
  fetchSections()
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary-dark:hover {
  background-color: #8b2424;
}
</style>
