/**
 * @fileoverview Composable for managing candidates from API
 * @description Provides reactive candidates data and searching functionality
 */

import { ref, computed, watch } from 'vue'
import { candidateService, type CandidateDto, type CandidateOption, type CandidateFilters, type CandidateResponse } from '@/services/candidate.service'

/**
 * Composable for candidates management
 */
export function useCandidates() {
  // Reactive state
  const candidates = ref<CandidateDto[]>([])
  const totalCount = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(50)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const searchQuery = ref('')
  const filters = ref<CandidateFilters>({})

  /**
   * Load candidates from API
   */
  const loadCandidates = async (forceRefresh: boolean = false) => {
    isLoading.value = true
    error.value = null
    
    try {
      const searchFilters: CandidateFilters = {
        ...filters.value,
        search: searchQuery.value || undefined,
        pageNumber: currentPage.value,
        pageSize: pageSize.value,
        activeOnly: true
      }

      const response: CandidateResponse = await candidateService.fetchCandidates(searchFilters, forceRefresh)
      
      candidates.value = response.items
      totalCount.value = response.totalCount
      
      console.log('✅ useCandidates: Candidates loaded from API:', candidates.value.length, 'candidates')
    } catch (err) {
      error.value = 'Failed to load candidates'
      console.error('❌ useCandidates: Error loading candidates from API:', err)
      candidates.value = []
      totalCount.value = 0
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Get candidates as dropdown options
   */
  const candidateOptions = computed((): CandidateOption[] => 
    candidates.value.map(candidate => ({
      id: candidate.id,
      examNumber: candidate.examNumber,
      fullName: `${candidate.firstName} ${candidate.lastName}`.trim(),
      firstName: candidate.firstName,
      lastName: candidate.lastName,
      centreId: candidate.centreId,
      examTypeId: candidate.examTypeId,
      isActive: candidate.isActive
    }))
  )

  /**
   * Search candidates
   */
  const searchCandidates = async (query: string) => {
    searchQuery.value = query
    currentPage.value = 1 // Reset to first page when searching
    await loadCandidates()
  }

  /**
   * Set filters
   */
  const setFilters = async (newFilters: Partial<CandidateFilters>) => {
    filters.value = { ...filters.value, ...newFilters }
    currentPage.value = 1 // Reset to first page when filtering
    await loadCandidates()
  }

  /**
   * Clear filters
   */
  const clearFilters = async () => {
    filters.value = {}
    searchQuery.value = ''
    currentPage.value = 1
    await loadCandidates()
  }

  /**
   * Go to specific page
   */
  const goToPage = async (page: number) => {
    currentPage.value = page
    await loadCandidates()
  }

  /**
   * Go to next page
   */
  const nextPage = async () => {
    if (hasNextPage.value) {
      await goToPage(currentPage.value + 1)
    }
  }

  /**
   * Go to previous page
   */
  const previousPage = async () => {
    if (hasPreviousPage.value) {
      await goToPage(currentPage.value - 1)
    }
  }

  /**
   * Computed pagination properties
   */
  const totalPages = computed(() => Math.ceil(totalCount.value / pageSize.value))
  const hasNextPage = computed(() => currentPage.value < totalPages.value)
  const hasPreviousPage = computed(() => currentPage.value > 1)

  /**
   * Get candidate by exam number
   */
  const getCandidateByExamNumber = (examNumber: string): CandidateDto | undefined => {
    return candidates.value.find(candidate => candidate.examNumber === examNumber)
  }

  /**
   * Get candidate by ID
   */
  const getCandidateById = (id: string): CandidateDto | undefined => {
    return candidates.value.find(candidate => candidate.id === id)
  }

  /**
   * Refresh candidates data
   */
  const refresh = () => loadCandidates(true)

  /**
   * Clear error state
   */
  const clearError = () => {
    error.value = null
  }

  return {
    // State
    candidates,
    candidateOptions,
    totalCount,
    currentPage,
    pageSize,
    totalPages,
    hasNextPage,
    hasPreviousPage,
    isLoading,
    error,
    searchQuery,
    filters,
    
    // Methods
    loadCandidates,
    searchCandidates,
    setFilters,
    clearFilters,
    goToPage,
    nextPage,
    previousPage,
    getCandidateByExamNumber,
    getCandidateById,
    refresh,
    clearError
  }
}

/**
 * Composable for searchable candidate dropdown
 */
export function useSearchableCandidates(initialFilters?: CandidateFilters) {
  const candidatesComposable = useCandidates()
  
  // Apply initial filters if provided
  if (initialFilters) {
    candidatesComposable.setFilters(initialFilters)
  }
  
  // Auto-load on mount
  candidatesComposable.loadCandidates()
  
  return {
    ...candidatesComposable,
    // Additional methods for searchable dropdown
    searchOptions: candidatesComposable.candidateOptions,
    search: candidatesComposable.searchCandidates,
    isSearching: candidatesComposable.isLoading
  }
}

/**
 * Composable for centre filtered candidates
 */
export function useCandidatesByCentre(centreId: string) {
  const candidatesComposable = useCandidates()
  
  // Set centre filter and load
  candidatesComposable.setFilters({ centreId })
  
  return {
    ...candidatesComposable,
    centreCandidates: candidatesComposable.candidates
  }
}

/**
 * Composable for exam type filtered candidates
 */
export function useCandidatesByExamType(examTypeId: string) {
  const candidatesComposable = useCandidates()
  
  // Set exam type filter and load
  candidatesComposable.setFilters({ examTypeId })
  
  return {
    ...candidatesComposable,
    examTypeCandidates: candidatesComposable.candidates
  }
}

/**
 * Debounced search composable for better performance
 */
export function useDebouncedCandidateSearch(debounceMs: number = 300) {
  const candidatesComposable = useSearchableCandidates()
  let debounceTimer: NodeJS.Timeout | null = null
  
  const debouncedSearch = (query: string) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer)
    }
    
    debounceTimer = setTimeout(() => {
      candidatesComposable.searchCandidates(query)
    }, debounceMs)
  }
  
  return {
    ...candidatesComposable,
    debouncedSearch
  }
}
