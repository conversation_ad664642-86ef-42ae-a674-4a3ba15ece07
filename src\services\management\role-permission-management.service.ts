/**
 * @fileoverview Role Permission Management Service for MANEB Results Management System
 * @description Service for managing role permissions with full CRUD operations and API alignment
 */

import apiClient from '../api-client';
import type { 
  RolePermissionDto, 
  CreateRolePermissionRequest, 
  UpdateRolePermissionRequest,
  RolePermissionFilterDto,
  PermissionAction,
  RecordStatus
} from '@/interfaces';

export class RolePermissionManagementService {
  /**
   * Get all role permissions
   */
  async getAllRolePermissions(): Promise<RolePermissionDto[]> {
    try {
      return await apiClient.get<RolePermissionDto[]>('/api/RolePermission');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch role permissions');
    }
  }

  /**
   * Get role permissions with pagination and filters - matching API schema
   */
  async getRolePermissionsPaginated(
    pageNumber: number = 1, 
    pageSize: number = 10,
    filters?: {
      Search?: string;
      SortBy?: string;
      SortDescending?: boolean;
      status?: RecordStatus;
    }
  ): Promise<RolePermissionDto[]> {
    try {
      const params = new URLSearchParams({
        PageNumber: pageNumber.toString(),
        PageSize: pageSize.toString()
      });

      // Add filters if provided
      if (filters?.Search) params.append('Search', filters.Search);
      if (filters?.SortBy) params.append('SortBy', filters.SortBy);
      if (filters?.SortDescending !== undefined) params.append('SortDescending', filters.SortDescending.toString());
      if (filters?.status) params.append('status', filters.status);

      return await apiClient.get<RolePermissionDto[]>(`/api/RolePermission/paginated?${params}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch role permissions');
    }
  }

  /**
   * Get role permission by ID
   */
  async getRolePermissionById(id: string): Promise<RolePermissionDto> {
    try {
      return await apiClient.get<RolePermissionDto>(`/api/RolePermission/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch role permission');
    }
  }

  /**
   * Create new role permission
   */
  async createRolePermission(permissionData: CreateRolePermissionRequest): Promise<RolePermissionDto> {
    try {
      return await apiClient.post<RolePermissionDto>('/api/RolePermission', permissionData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create role permission');
    }
  }

  /**
   * Update role permission
   */
  async updateRolePermission(id: string, permissionData: UpdateRolePermissionRequest): Promise<void> {
    try {
      await apiClient.put(`/api/RolePermission/${id}`, permissionData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update role permission');
    }
  }

  /**
   * Delete role permission
   */
  async deleteRolePermission(id: string): Promise<void> {
    try {
      await apiClient.delete(`/api/RolePermission/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete role permission');
    }
  }

  /**
   * Approve role permission - matching API endpoint
   */
  async approveRolePermission(id: string): Promise<void> {
    try {
      await apiClient.put(`/api/RolePermission/${id}/approve`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to approve role permission');
    }
  }

  /**
   * Get permissions for a specific role
   */
  async getRolePermissionsByRoleId(roleId: string): Promise<RolePermissionDto[]> {
    try {
      const allPermissions = await this.getAllRolePermissions();
      return allPermissions.filter(permission => permission.roleId === roleId);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch role permissions');
    }
  }

  /**
   * Get permissions for a specific section
   */
  async getRolePermissionsBySectionId(sectionId: string): Promise<RolePermissionDto[]> {
    try {
      const allPermissions = await this.getAllRolePermissions();
      return allPermissions.filter(permission => permission.sectionID === sectionId);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch section permissions');
    }
  }

  /**
   * Bulk create role permissions
   */
  async bulkCreateRolePermissions(permissions: CreateRolePermissionRequest[]): Promise<RolePermissionDto[]> {
    try {
      const results: RolePermissionDto[] = [];
      
      for (const permission of permissions) {
        const result = await this.createRolePermission(permission);
        results.push(result);
      }
      
      return results;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to bulk create role permissions');
    }
  }

  /**
   * Bulk delete role permissions by role ID
   */
  async bulkDeleteRolePermissionsByRoleId(roleId: string): Promise<void> {
    try {
      const permissions = await this.getRolePermissionsByRoleId(roleId);
      
      for (const permission of permissions) {
        if (permission.id) {
          await this.deleteRolePermission(permission.id);
        }
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to bulk delete role permissions');
    }
  }

  /**
   * Replace role permissions (delete all existing and create new ones)
   */
  async replaceRolePermissions(roleId: string, newPermissions: CreateRolePermissionRequest[]): Promise<RolePermissionDto[]> {
    try {
      // Delete existing permissions
      await this.bulkDeleteRolePermissionsByRoleId(roleId);
      
      // Create new permissions
      return await this.bulkCreateRolePermissions(newPermissions);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to replace role permissions');
    }
  }

  /**
   * Check if role has permission for section and action
   */
  async checkRolePermission(roleId: string, sectionId: string, action: PermissionAction): Promise<boolean> {
    try {
      const rolePermissions = await this.getRolePermissionsByRoleId(roleId);
      const permission = rolePermissions.find(rp => 
        rp.sectionID === sectionId && 
        (rp.action === action || rp.action === 'All')
      );
      
      return permission ? permission.canAccess : false;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to check role permission');
    }
  }

  /**
   * Filter role permissions using local filtering
   */
  async filterRolePermissions(filters: RolePermissionFilterDto): Promise<RolePermissionDto[]> {
    try {
      let permissions = await this.getAllRolePermissions();

      if (filters.searchQuery) {
        const searchTerm = filters.searchQuery.toLowerCase();
        permissions = permissions.filter(permission => 
          permission.section?.name?.toLowerCase().includes(searchTerm) ||
          permission.action?.toLowerCase().includes(searchTerm)
        );
      }

      if (filters.roleId && filters.roleId !== 'All') {
        permissions = permissions.filter(permission => permission.roleId === filters.roleId);
      }

      if (filters.sectionId && filters.sectionId !== 'All') {
        permissions = permissions.filter(permission => permission.sectionID === filters.sectionId);
      }

      if (filters.action && filters.action !== 'All') {
        permissions = permissions.filter(permission => permission.action === filters.action);
      }

      if (filters.canAccess !== undefined && filters.canAccess !== 'All') {
        permissions = permissions.filter(permission => permission.canAccess === filters.canAccess);
      }

      if (filters.status && filters.status !== 'All') {
        permissions = permissions.filter(permission => permission.status === filters.status);
      }

      return permissions;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to filter role permissions');
    }
  }
}

export const rolePermissionManagementService = new RolePermissionManagementService();
