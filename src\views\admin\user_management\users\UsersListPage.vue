<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- Breadcrumbs -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        <li class="inline-flex items-center">
          <router-link to="/admin" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-maneb-primary">
            Admin
          </router-link>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <router-link to="/admin/user-management" class="ml-1 text-sm font-medium text-gray-700 hover:text-maneb-primary md:ml-2">User Management</router-link>
          </div>
        </li>
        <li aria-current="page">
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Users</span>
          </div>
        </li>
      </ol>
    </nav>

    <!-- Header -->
    <div class="mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">User Management</h1>
          <p class="mt-1 text-sm text-gray-600">Manage system users and their permissions</p>
        </div>
        <router-link
          :to="{ name: 'admin.user-management.users.create' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add New User
        </router-link>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 gap-4 mb-6 sm:grid-cols-2 lg:grid-cols-4">
      <div class="bg-white shadow-sm border border-gray-200 rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex items-center justify-center w-12 h-12 bg-blue-50 rounded-lg">
            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Users</p>
            <p class="text-2xl font-bold text-black">{{ totalUsers }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white shadow-sm border border-gray-200 rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex items-center justify-center w-12 h-12 bg-green-50 rounded-lg">
            <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Active Users</p>
            <p class="text-2xl font-bold text-black">{{ activeUsers }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white shadow-sm border border-gray-200 rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex items-center justify-center w-12 h-12 bg-yellow-50 rounded-lg">
            <svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Pending Approval</p>
            <p class="text-2xl font-bold text-black">{{ pendingUsers }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white shadow-sm border border-gray-200 rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex items-center justify-center w-12 h-12 bg-red-50 rounded-lg">
            <svg class="h-6 w-6 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Admin Users</p>
            <p class="text-2xl font-bold text-black">{{ adminUsers }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-4 mb-6">
      <div class="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search users..."
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-maneb-primary focus:border-transparent"
          >
        </div>
        <div>
          <select
            v-model="statusFilter"
            class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-maneb-primary focus:border-transparent"
          >
            <option value="All">All Status</option>
            <option value="Approved">Approved</option>
            <option value="Unapproved">Unapproved</option>
            <option value="SecondApproved">Second Approved</option>
            <option value="Rejected">Rejected</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Role
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-if="isLoading">
              <td colspan="5" class="px-6 py-12 text-center">
                <div class="flex flex-col items-center justify-center space-y-3">
                  <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-maneb-primary"></div>
                  <span class="text-sm text-gray-600">Loading users...</span>
                </div>
              </td>
            </tr>

            <tr v-else-if="paginatedUsers.length === 0">
              <td colspan="5" class="px-6 py-12 text-center">
                <div class="flex flex-col items-center justify-center space-y-3">
                  <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  <div class="text-sm text-gray-600">
                    <p class="font-medium">No users found</p>
                    <p class="mt-1">Try adjusting your search or filter criteria</p>
                  </div>
                </div>
              </td>
            </tr>

            <tr v-for="user in paginatedUsers" :key="user.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center space-x-4">
                  <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                    <span class="text-sm font-medium text-maneb-primary">
                      {{ getUserInitials(user) }}
                    </span>
                  </div>
                  <div>
                    <p class="text-sm font-medium text-black">
                      {{ user.fullName || `${user.firstName} ${user.lastName}` }}
                    </p>
                    <p class="text-sm text-gray-500">
                      {{ user.email || 'No email' }}
                    </p>
                  </div>
                </div>
              </td>

              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusBadgeClass(user.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ getStatusDisplayText(user.status) }}
                </span>
              </td>

              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-black">
                  <template v-if="user.roles && user.roles.length > 0">
                    <div class="flex flex-wrap gap-1">
                      <span v-for="role in user.roles" :key="role.id" class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                        {{ role.name }}
                      </span>
                    </div>
                  </template>
                  <span v-else class="text-gray-500">No roles assigned</span>
                </div>
              </td>

              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(user.dateCreated) }}
              </td>

              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-1">
                  <button
                    @click="viewUser(user)"
                    class="text-blue-600 hover:text-blue-900 p-2 rounded-md hover:bg-blue-50 transition-colors duration-200"
                    title="View User Details"
                  >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <router-link
                    :to="{ name: 'admin.user-management.users.edit', params: { id: user.id } }"
                    class="text-maneb-primary hover:text-red-700 p-2 rounded-md hover:bg-red-50 transition-colors duration-200"
                    title="Edit User"
                  >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </router-link>
                  <router-link
                    :to="{ name: 'admin.user-management.users.status', params: { id: user.id } }"
                    class="text-green-600 hover:text-green-700 p-2 rounded-md hover:bg-green-50 transition-colors duration-200"
                    title="Manage Status"
                  >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </router-link>
                  <router-link
                    :to="{ name: 'admin.user-management.users.roles', params: { id: user.id } }"
                    class="text-purple-600 hover:text-purple-700 p-2 rounded-md hover:bg-purple-50 transition-colors duration-200"
                    title="Assign Role"
                  >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                  </router-link>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Modals removed - now using page-based forms -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore, useRoleStore, useAuthStore } from '@/store'
import type { UserDto, RecordStatus } from '@/interfaces'


// Router
const router = useRouter()

// Stores
const userStore = useUserStore()
const roleStore = useRoleStore()
const authStore = useAuthStore()

// Reactive data
const isLoading = ref(false)
const searchQuery = ref('')
const statusFilter = ref<RecordStatus | 'All'>('All')
const currentPage = ref(1)
const itemsPerPage = ref(10)

// Navigation is now handled by router

// Computed properties
const totalUsers = computed(() => userStore.users.length)
const activeUsers = computed(() => userStore.users.filter((u: any) => u.status === 'Approved' || u.status === 'SecondApproved').length)
const pendingUsers = computed(() => userStore.users.filter((u: any) => u.status === 'Unapproved').length)
const adminUsers = computed(() => userStore.users.filter((u: any) => u.status === 'SecondApproved').length)

const filteredUsers = computed(() => {
  let filtered = userStore.users

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter((user: any) =>
      user.firstName?.toLowerCase().includes(query) ||
      user.lastName?.toLowerCase().includes(query) ||
      user.email?.toLowerCase().includes(query) ||
      user.fullName?.toLowerCase().includes(query)
    )
  }

  // Apply status filter
  if (statusFilter.value !== 'All') {
    filtered = filtered.filter((user: any) => user.status === statusFilter.value)
  }

  return filtered
})

const paginatedUsers = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value
  return filteredUsers.value.slice(start, end)
})

// Methods
const getUserInitials = (user: UserDto): string => {
  const firstName = user.firstName || ''
  const lastName = user.lastName || ''
  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
}

const getStatusBadgeClass = (status?: RecordStatus): string => {
  switch (status) {
    case 'Approved':
      return 'bg-green-100 text-green-800'
    case 'SecondApproved':
      return 'bg-blue-100 text-blue-800'
    case 'Unapproved':
      return 'bg-yellow-100 text-yellow-800'
    case 'Rejected':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusDisplayText = (status?: RecordStatus): string => {
  switch (status) {
    case 'Approved':
      return 'Approved'
    case 'SecondApproved':
      return 'Admin'
    case 'Unapproved':
      return 'Pending'
    case 'Rejected':
      return 'Rejected'
    default:
      return 'Unknown'
  }
}

const formatDate = (date?: Date | string): string => {
  if (!date) return 'N/A'
  return new Date(date).toLocaleDateString()
}

const viewUser = (user: UserDto) => {
  router.push({ name: 'admin.user-management.users.detail', params: { id: user.id } })
}

// All user management actions now use page-based navigation

// Lifecycle
onMounted(async () => {
  isLoading.value = true
  try {
    console.log('Loading users and roles...')
    console.log('Auth token available:', !!authStore.token)
    console.log('User authenticated:', authStore.isAuthenticated)

    await Promise.all([
      userStore.fetchUsers(),
      roleStore.fetchRoles()
    ])

    console.log('Data loaded successfully')
    console.log('Users count:', userStore.users.length)
    console.log('Roles count:', roleStore.roles.length)
  } catch (error: any) {
    console.error('Error loading data:', error)
    console.error('Error details:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    })
  } finally {
    isLoading.value = false
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>