<template>
  <ProtectedRoute
    :module="PermissionModule.SCORE_ENTRY"
    :action="PermissionAction.VIEW"
  >
    <div class="min-h-screen bg-gray-50">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-3">
      <!-- Breadcrumb -->
      <nav class="flex mb-2" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-2">
          <li class="inline-flex items-center">
            <a href="/admin/dashboard" class="inline-flex items-center text-xs font-medium text-gray-700 hover:text-maneb-primary">
              <svg class="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
              </svg>
              Admin
            </a>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
              <span class="ml-1 text-xs font-medium text-gray-500 md:ml-1">Score Entry</span>
            </div>
          </li>
        </ol>
      </nav>

      <!-- Page Header -->
      <div class="mb-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Score Entry</h1>
            <p class="mt-1 text-sm text-gray-600">
              <span v-if="hasRouteParams">Select filters and enter examination scores</span>
              <span v-else-if="currentStage === 'filtering'">Select examination criteria to begin score entry</span>
              <span v-else>Enter examination scores for registered students</span>
            </p>
          </div>
          <div class="flex items-center space-x-3">
            <!-- Edit Filters Button (only show when in score entry stage) -->
            <button
              v-if="currentStage === 'scoreEntry' && selectedFilters"
              @click="handleEditFilters"
              class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:ring-2 focus:outline-none focus:ring-maneb-primary/50 transition-colors duration-200"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              Edit Filters
            </button>
          </div>
        </div>
      </div>

      <!-- New Main Page Layout (when route params provided) -->
      <div v-if="hasRouteParams">
        <ScoreEntryMainPage
          :key="`${routeExamType}-${routeExamYear}`"
          :route-exam-type="routeExamType"
          :route-exam-year="routeExamYear"
          :has-route-params="hasRouteParams"
        />
      </div>

      <!-- Legacy Two-Stage Layout (when no route params) -->
      <div v-else>
        <!-- Stage 1: Filtering Page -->
        <div v-if="currentStage === 'filtering'" class="space-y-4">
          <FilteringStage
            @filters-selected="handleFiltersSelected"
            :is-loading="isTransitioning"
            :current-filters="selectedFilters"
            :route-exam-type="routeExamType"
            :route-exam-year="routeExamYear"
            :has-route-params="hasRouteParams"
          />
        </div>

        <!-- Stage 2: Score Entry Table -->
        <div v-else-if="currentStage === 'scoreEntry'">
          <ScoreEntryStage
            :selected-filters="selectedFilters"
            @edit-filters="handleEditFilters"
            @score-updated="handleScoreUpdated"
          />
        </div>
      </div>

    </div>

    <!-- Score Entry Modal removed - now using inline score entry -->
    </div>
  </ProtectedRoute>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import ProtectedRoute from '@/components/auth/ProtectedRoute.vue'
import FilteringStage from './components/FilteringStage.vue'
import ScoreEntryStage from './components/ScoreEntryStage.vue'
import ScoreEntryMainPage from './components/ScoreEntryMainPage.vue'


import { PermissionModule, PermissionAction } from '@/utils/permissions'
import sweetAlert from '@/utils/ui/sweetAlert'

// Types
interface FilterSelection {
  division: string
  district: string
  center: string
  school: string
  subject: string
  examType: string
  examNumber: string
  examYear: number
}

// Get route parameters
const route = useRoute()

// Reactive state - Start with filtering stage to establish context
const currentStage = ref<'filtering' | 'scoreEntry'>('filtering')
const selectedFilters = ref<FilterSelection | null>(null)
const isTransitioning = ref(false)

// Check if we have route parameters for exam type and year
const hasRouteParams = ref(false)
const routeExamType = ref<string>('')
const routeExamYear = ref<number>(new Date().getFullYear())
// Initialize route parameters on mount
onMounted(() => {
  initializeFromRoute()
})

// Watch for route changes
watch(() => route.params, () => {
  initializeFromRoute()
}, { deep: true })

// Initialize filters from route parameters
const initializeFromRoute = () => {
  const examType = route.params.examType as string
  const year = route.params.year as string

  console.log('🔍 initializeFromRoute called with:', { examType, year, routePath: route.path })

  if (examType && year) {
    console.log('✅ Route params detected:', examType, year)
    hasRouteParams.value = true
    routeExamType.value = examType
    routeExamYear.value = parseInt(year)

    console.log('📝 Updated route values:', {
      hasRouteParams: hasRouteParams.value,
      routeExamType: routeExamType.value,
      routeExamYear: routeExamYear.value
    })

    // If we have route params, skip filtering stage and go directly to score entry
    // with pre-populated exam type and year
    if (selectedFilters.value) {
      selectedFilters.value.examType = examType
      selectedFilters.value.examYear = parseInt(year)
    }
  } else {
    console.log('❌ No route params found, using default mode')
    hasRouteParams.value = false
    routeExamType.value = ''
    routeExamYear.value = new Date().getFullYear()
  }
}

// Computed properties for stage management

// Methods
const handleFiltersSelected = async (filters: FilterSelection) => {
  isTransitioning.value = true

  try {
    // Simulate API validation/loading
    await new Promise(resolve => setTimeout(resolve, 1000))

    selectedFilters.value = filters
    currentStage.value = 'scoreEntry'

    // sweetAlert.success(
    //   'Filters Applied',
    //   'Score entry table is now ready for the selected criteria.'
    // )
  } catch (error) {
    console.error('Error applying filters:', error)
    sweetAlert.error('Error', 'Failed to apply filters. Please try again.')
  } finally {
    isTransitioning.value = false
  }
}

const handleEditFilters = () => {
  console.log('🔄 Editing filters, current selectedFilters:', selectedFilters.value)
  currentStage.value = 'filtering'
  // Keep the selected filters so they can be pre-populated for editing
}

const handleScoreUpdated = (data: any) => {
  console.log('Score updated:', data)
  // sweetAlert.success(
  //   'Score Updated',
  //   'The student score has been successfully updated.'
  // )
}

// No longer needed since filters are reactive in the new component

// Score entry is now handled inline by ScoreEntryStage component
</script>

<style scoped>
.bg-maneb-primary {
  background-color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.text-maneb-primary {
  color: #a12c2c;
}

.hover\:text-maneb-primary:hover {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary\/50:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

/* Responsive table improvements */
@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.875rem;
  }
}
</style>
