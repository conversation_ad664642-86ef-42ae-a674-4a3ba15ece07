import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { paperService } from '@/services/paper.service'
import type { PaperDto } from '@/services/paper.service'
import { useAuthStore } from './auth.store'

export const usePaperStore = defineStore('paper', () => {
  // State
  const papers = ref<PaperDto[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const papersCache = ref(new Map<string, PaperDto[]>())

  // Getters
  const getPaperById = computed(() => {
    return (id: string) => papers.value.find(paper => paper.id === id)
  })

  const getPapersBySubject = computed(() => {
    return (subjectId: string) => papers.value.filter(paper => 
      paper.subjectId === subjectId
    )
  })

  const getActivePapers = computed(() => {
    return papers.value.filter(paper => paper.isActive !== false)
  })

  const hasError = computed(() => !!error.value)

  const getPaperName = computed(() => {
    return (paperId: string) => {
      const paper = getPaperById.value(paperId)
      return paper ? paper.name : ''
    }
  })

  // Actions
  const clearError = () => {
    error.value = null
  }

  const clearPapers = () => {
    papers.value = []
  }

  const clearCache = () => {
    papersCache.value.clear()
  }

  /**
   * Fetch papers with optional filtering
   */
  const fetchPapers = async (filters: {
    subjectId?: string;
    examTypeId?: string;
    pageSize?: number;
    search?: string;
  } = {}) => {
    isLoading.value = true
    error.value = null
    
    try {
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        throw new Error('Authentication required')
      }

      // Create cache key for this filter combination
      const cacheKey = JSON.stringify(filters)
      
      // Check cache first
      if (papersCache.value.has(cacheKey)) {
        papers.value = papersCache.value.get(cacheKey)!
        console.log('✅ Papers loaded from cache:', papers.value.length, 'items')
        return papers.value
      }

      console.log('🔍 Fetching papers with filters:', filters)
      const response = await paperService.fetchPapers(filters)
      
      papers.value = response.items
      papersCache.value.set(cacheKey, response.items)
      
      console.log('✅ Papers loaded from API:', response.items.length, 'items')
      return response.items
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch papers'
      console.error('❌ Error fetching papers:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Load papers for a specific subject
   */
  const loadPapersForSubject = async (subjectId: string) => {
    if (!subjectId) {
      console.warn('⚠️ No subject ID provided for paper loading')
      return []
    }

    try {
      console.log('🔍 Loading papers for subject:', subjectId)
      
      const filters = {
        subjectId,
        pageSize: 100
      }
      
      const response = await fetchPapers(filters)
      console.log('✅ Papers loaded for subject:', subjectId, response.length, 'papers')
      return response
    } catch (err: any) {
      console.error('❌ Error loading papers for subject:', err)
      throw err
    }
  }

  /**
   * Load papers for a specific exam type
   */
  const loadPapersForExamType = async (examTypeId: string) => {
    if (!examTypeId) {
      console.warn('⚠️ No exam type ID provided for paper loading')
      return []
    }

    try {
      console.log('🔍 Loading papers for exam type:', examTypeId)
      
      const filters = {
        examTypeId,
        pageSize: 200
      }
      
      const response = await fetchPapers(filters)
      console.log('✅ Papers loaded for exam type:', examTypeId, response.length, 'papers')
      return response
    } catch (err: any) {
      console.error('❌ Error loading papers for exam type:', err)
      throw err
    }
  }

  /**
   * Search papers by name
   */
  const searchPapers = async (query: string, subjectId?: string) => {
    try {
      console.log('🔍 Searching papers with query:', query, 'for subject:', subjectId)
      
      const filters: any = {
        search: query,
        pageSize: 100
      }
      
      if (subjectId) {
        filters.subjectId = subjectId
      }
      
      const response = await fetchPapers(filters)
      console.log('✅ Paper search results:', response.length, 'papers')
      return response
    } catch (err: any) {
      error.value = err.message || 'Failed to search papers'
      console.error('❌ Error searching papers:', err)
      throw err
    }
  }

  /**
   * Get paper options for dropdowns
   */
  const getPaperOptions = (subjectId?: string) => {
    let filteredPapers = getActivePapers.value
    
    if (subjectId) {
      filteredPapers = getPapersBySubject.value(subjectId)
    }
    
    return filteredPapers.map(paper => ({
      id: paper.id,
      name: paper.name,
      description: paper.description || '',
      value: paper.id,
      subjectId: paper.subjectId
    }))
  }

  /**
   * Get papers by subject and exam type
   */
  const getPapersBySubjectAndExamType = async (subjectId: string, examTypeId: string) => {
    try {
      const filters = {
        subjectId,
        examTypeId,
        pageSize: 50
      }

      const response = await fetchPapers(filters)
      return response
    } catch (err: any) {
      throw err
    }
  }

  /**
   * Reset store state
   */
  const resetStore = () => {
    papers.value = []
    isLoading.value = false
    error.value = null
    papersCache.value.clear()
  }

  return {
    // State
    papers,
    isLoading,
    error,
    
    // Getters
    getPaperById,
    getPapersBySubject,
    getActivePapers,
    getPaperName,
    hasError,
    
    // Actions
    clearError,
    clearPapers,
    clearCache,
    fetchPapers,
    loadPapersForSubject,
    loadPapersForExamType,
    searchPapers,
    getPaperOptions,
    getPapersBySubjectAndExamType,
    resetStore
  }
})
