import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { examTypeService } from '@/services/exam-type.service'
import type { ExamTypeDto } from '@/services/exam-type.service'
import { useAuthStore } from './auth.store'

export const useExamTypeStore = defineStore('examType', () => {
  // State
  const examTypes = ref<ExamTypeDto[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const examTypesLoaded = ref(false)

  // Getters
  const getExamTypeById = computed(() => {
    return (id: string) => examTypes.value.find(examType => examType.id === id)
  })

  const getActiveExamTypes = computed(() => {
    return examTypes.value.filter(examType => examType.isActive !== false)
  })

  const getPrimarySchoolExamTypes = computed(() => {
    return examTypes.value.filter(examType => 
      examType.name.toLowerCase().includes('primary') || 
      examType.id === 'PSLCE' ||
      examType.name.toLowerCase().includes('plsce')
    )
  })

  const getSecondaryExamTypes = computed(() => {
    return examTypes.value.filter(examType => 
      !examType.name.toLowerCase().includes('primary') && 
      examType.id !== 'PSLCE' &&
      !examType.name.toLowerCase().includes('plsce')
    )
  })

  const hasError = computed(() => !!error.value)

  const getExamTypeName = computed(() => {
    return (examTypeId: string) => {
      const examType = getExamTypeById.value(examTypeId)
      return examType ? examType.name : ''
    }
  })

  const isPrimarySchoolExamType = computed(() => {
    return (examTypeId: string) => {
      const examType = getExamTypeById.value(examTypeId)
      if (!examType) return false
      
      return examType.name.toLowerCase().includes('primary') || 
             examType.id === 'PSLCE' ||
             examType.name.toLowerCase().includes('plsce')
    }
  })

  // Actions
  const clearError = () => {
    error.value = null
  }

  const clearExamTypes = () => {
    examTypes.value = []
    examTypesLoaded.value = false
  }

  /**
   * Fetch all exam types
   */
  const fetchExamTypes = async (filters: {
    pageSize?: number;
    search?: string;
  } = {}) => {
    // Return cached data if already loaded
    if (examTypesLoaded.value && examTypes.value.length > 0) {
      console.log('✅ Exam types loaded from cache:', examTypes.value.length, 'items')
      return examTypes.value
    }

    isLoading.value = true
    error.value = null
    
    try {
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        throw new Error('Authentication required')
      }

      console.log('🔍 Fetching exam types with filters:', filters)
      const response = await examTypeService.fetchExamTypes(filters)
      
      examTypes.value = response.items
      examTypesLoaded.value = true
      
      console.log('✅ Exam types loaded from API:', response.items.length, 'items')
      return response.items
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch exam types'
      console.error('❌ Error fetching exam types:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Search exam types by name
   */
  const searchExamTypes = async (query: string) => {
    try {
      // Searching exam types (console logging removed)

      const filters = {
        search: query,
        pageSize: 50
      }

      const response = await examTypeService.fetchExamTypes(filters)
      // Search results (console logging removed)
      return response.items
    } catch (err: any) {
      error.value = err.message || 'Failed to search exam types'
      // Error logging removed
      throw err
    }
  }

  /**
   * Get exam type options for dropdowns
   */
  const getExamTypeOptions = () => {
    return getActiveExamTypes.value.map(examType => ({
      id: examType.id,
      name: examType.name,
      description: examType.description || '',
      value: examType.id
    }))
  }

  /**
   * Load exam types if not already loaded
   */
  const ensureExamTypesLoaded = async () => {
    if (!examTypesLoaded.value) {
      await fetchExamTypes({ pageSize: 50 })
    }
    return examTypes.value
  }

  /**
   * Check if an exam type is primary school type
   */
  const checkIsPrimarySchoolExamType = (examTypeId: string): boolean => {
    return isPrimarySchoolExamType.value(examTypeId)
  }

  /**
   * Reset store state
   */
  const resetStore = () => {
    examTypes.value = []
    isLoading.value = false
    error.value = null
    examTypesLoaded.value = false
  }

  return {
    // State
    examTypes,
    isLoading,
    error,
    examTypesLoaded,
    
    // Getters
    getExamTypeById,
    getActiveExamTypes,
    getPrimarySchoolExamTypes,
    getSecondaryExamTypes,
    getExamTypeName,
    isPrimarySchoolExamType,
    hasError,
    
    // Actions
    clearError,
    clearExamTypes,
    fetchExamTypes,
    searchExamTypes,
    getExamTypeOptions,
    ensureExamTypesLoaded,
    checkIsPrimarySchoolExamType,
    resetStore
  }
})
