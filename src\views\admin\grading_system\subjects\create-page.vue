<template>
  <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-6">
    <!-- Header -->
    <div class="mb-8">
      <nav class="flex mb-5" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 text-sm font-medium md:space-x-2">
          <li class="inline-flex items-center">
            <router-link to="/admin/dashboard" class="inline-flex items-center text-gray-700 hover:text-maneb-primary transition-colors duration-200">
              <svg class="w-5 h-5 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
              </svg>
              Admin Dashboard
            </router-link>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <router-link to="/admin/grading-system" class="ml-1 text-gray-700 hover:text-maneb-primary md:ml-2 transition-colors duration-200">
                Grading System
              </router-link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="ml-1 text-gray-400 md:ml-2" aria-current="page">Create Subject</span>
            </div>
          </li>
        </ol>
      </nav>
      <h1 class="text-3xl font-bold text-gray-900">Create Subject</h1>
      <p class="mt-2 text-sm text-gray-600">Add a new subject to the grading system</p>
    </div>

    <!-- Create Form -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Subject Information</h3>
        <p class="mt-1 text-sm text-gray-600">Please fill in all required fields to create a new subject.</p>
      </div>

      <form @submit.prevent="handleSubmit" class="px-6 py-6 space-y-6">
        <!-- Subject Name -->
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700">Subject Name <span class="text-red-500">*</span></label>
          <input id="name" v-model="formData.name" type="text" required class="mt-1 block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary" placeholder="Enter subject name" />
          <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</p>
        </div>

        <!-- Subject Code -->
        <div>
          <label for="code" class="block text-sm font-medium text-gray-700">Subject Code <span class="text-red-500">*</span></label>
          <input id="code" v-model="formData.code" type="text" required class="mt-1 block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary" placeholder="Enter subject code" />
          <p v-if="errors.code" class="mt-1 text-sm text-red-600">{{ errors.code }}</p>
        </div>

        <!-- Description -->
        <div>
          <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
          <textarea id="description" v-model="formData.description" rows="3" class="mt-1 block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary" placeholder="Enter description (optional)" />
        </div>

        <!-- Active Status -->
        <div class="flex items-center">
          <input id="isActive" v-model="formData.isActive" type="checkbox" class="h-4 w-4 text-maneb-primary focus:ring-maneb-primary border-gray-300 rounded" />
          <label for="isActive" class="ml-2 block text-sm text-gray-900">Active (subject is currently in use)</label>
        </div>

        <!-- Error Display -->
        <div v-if="submitError" class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">Error creating subject</h3>
              <div class="mt-2 text-sm text-red-700">{{ submitError }}</div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <router-link :to="{ name: 'admin.grading-system' }" class="inline-flex items-center px-6 py-3 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary transition-colors duration-200">
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Cancel
          </router-link>
          <button type="submit" :disabled="isSubmitting" class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 shadow-sm">
            <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else class="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
            </svg>
            {{ isSubmitting ? 'Creating Subject...' : 'Create Subject' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useGradingStore } from '@/store'
import type { CreateSubjectRequest } from '@/interfaces'
import sweetAlert from '@/utils/sweetAlert'

// Router
const router = useRouter()

// Stores
const gradingStore = useGradingStore()

// Reactive state
const formData = reactive<{
  name: string
  code: string
  description: string
  isActive: boolean
}>({
  name: '',
  code: '',
  description: '',
  isActive: true
})

const errors = reactive<Record<string, string>>({})
const submitError = ref<string | null>(null)
const isSubmitting = ref(false)

// Methods
const validateForm = (): boolean => {
  Object.keys(errors).forEach(key => delete errors[key])
  let isValid = true

  if (!formData.name.trim()) {
    errors.name = 'Subject name is required'
    isValid = false
  }

  if (!formData.code.trim()) {
    errors.code = 'Subject code is required'
    isValid = false
  }

  return isValid
}

const handleSubmit = async () => {
  if (!validateForm()) return

  try {
    isSubmitting.value = true
    submitError.value = null

    const submitData: CreateSubjectRequest = {
      name: formData.name.trim(),
      code: formData.code.trim(),
      description: formData.description.trim() || undefined,
      isActive: formData.isActive
    }

    await gradingStore.createSubject(submitData)
    await sweetAlert.success('Subject created successfully!')
    router.push({ name: 'admin.grading-system' })
  } catch (error: any) {
    submitError.value = error.message || 'Failed to create subject'
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style scoped>
.bg-maneb-primary { background-color: #a12c2c; }
.text-maneb-primary { color: #a12c2c; }
.bg-maneb-primary-dark { background-color: #8b2424; }
.border-maneb-primary { border-color: #a12c2c; }
.focus\:ring-maneb-primary:focus { --tw-ring-color: #a12c2c; }
.focus\:border-maneb-primary:focus { border-color: #a12c2c; }
.hover\:bg-maneb-primary-dark:hover { background-color: #8b2424; }
</style>
