import type { AxiosResponse, AxiosError } from 'axios'
import type {
  AuditLog,
  GradeBoundaryDto,
  CreateGradeBoundaryRequest,
  UpdateGradeBoundaryRequest,
  GradeBoundaryFilters,
  PaperDto,
  ScoreEntry,
  ScoreEntryDto,
  ScoreEntryApiDto,
  CreateScoreEntryRequest,
  UpdateScoreEntryRequest,
  ScoreApprovalRequest,
  ScoreVerificationRequest,
  ApiError
} from '@/interfaces/external/results-api.interface'
import { BaseApiClient } from './base-api-client'

/**
 * Grading API Service for external grading system integration
 * Extends BaseApiClient with grading-specific functionality
 */
export class GradingApiService extends BaseApiClient {
  constructor() {
    // Use the main API base URL since we've consolidated to a single API
    const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://41.79.88.105:88'

    // Grading API Service Configuration (console logging removed)

    super({
      baseURL,
      timeout: 30000,
      enableDebugLogging: import.meta.env.DEV,
      errorFormatter: (error: AxiosError) => this.formatGradingApiError(error)
    })
  }

  /**
   * Custom error formatter for grading API
   */
  private formatGradingApiError(error: AxiosError): ApiError {
    return {
      message: (error.response?.data as any)?.message || error.message || 'Grading API request failed',
      statusCode: error.response?.status || 500,
      details: error.response?.statusText || 'Unknown Error',
      timestamp: new Date()
    }
  }



  // Validate ScoreEntryDto format
  private validateScoreEntryDto(dto: ScoreEntryDto): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!dto.scriptId || dto.scriptId <= 0) {
      errors.push('scriptId must be a positive number')
    }

    if (!dto.userId || typeof dto.userId !== 'string') {
      errors.push('userId must be a valid string')
    }

    if (dto.score === null || dto.score === undefined || dto.score < 0 || dto.score > 100) {
      errors.push('score must be between 0 and 100')
    }

    if (!dto.scoreType || typeof dto.scoreType !== 'string') {
      errors.push('scoreType is required and must be a string')
    }

    if (!dto.enteredByUserId || typeof dto.enteredByUserId !== 'string') {
      errors.push('enteredByUserId must be a valid string')
    }

    if (!dto.status || typeof dto.status !== 'string') {
      errors.push('status is required and must be a string')
    }

    if (!dto.timestamp || !(dto.timestamp instanceof Date)) {
      errors.push('timestamp must be a valid Date object')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  // Audit Logs API
  async getAuditLogs(): Promise<AuditLog[]> {
    const response: AxiosResponse<AuditLog[]> = await this.client.get('/api/AuditLogs')
    return response.data
  }

  async createGradeBoundary(data: CreateGradeBoundaryRequest): Promise<void> {
    try {
      await this.client.post('/api/GradeBoundary', data)
    } catch (error: any) {
      throw error
    }
  }

  async getGradeBoundaries(filters?: GradeBoundaryFilters): Promise<GradeBoundaryDto[]> {
    const params = new URLSearchParams()
    if (filters?.examLevel) params.append('examLevel', filters.examLevel)
    if (filters?.subjectCode) params.append('subjectCode', filters.subjectCode)

    const response: AxiosResponse<GradeBoundaryDto[]> = await this.client.get(
      `/api/GradeBoundary${params.toString() ? `?${params.toString()}` : ''}`
    )
    return response.data
  }

  async updateGradeBoundary(id: string, data: UpdateGradeBoundaryRequest): Promise<void> {
    await this.client.put(`/api/GradeBoundary/${id}`, data)
  }

  async deleteGradeBoundary(id: string): Promise<void> {
    await this.client.delete(`/api/GradeBoundary/${id}`)
  }

  async getGradeAuditLogs(): Promise<AuditLog[]> {
    const response: AxiosResponse<AuditLog[]> = await this.client.get('/api/Grades/audit-logs')
    return response.data
  }

  // Papers API
  async getPapers(examLevel?: string): Promise<PaperDto[]> {
    const params = new URLSearchParams()
    if (examLevel) params.append('examLevel', examLevel)

    const response: AxiosResponse<PaperDto[]> = await this.client.get(
      `/api/paper${params.toString() ? `?${params.toString()}` : ''}`
    )
    return response.data
  }

  // Scores API
  async getScores(): Promise<ScoreEntry[]> {
    const response: AxiosResponse<ScoreEntry[]> = await this.client.get('/api/Scores')
    return response.data
  }

  async getPaginatedScoreEntries(params: {
    creator?: string
    centerNo?: string
    schoolId?: string
    examTypeId?: string
    examYear?: number
    subjectId?: string
    pageNumber?: number
    pageSize?: number
  }): Promise<{
    totalCount: number
    pageNumber: number
    pageSize: number
    totalPages: number
    hasNextPage: boolean
    hasPreviousPage: boolean
    items: ScoreEntryApiDto[]
  }> {
    const searchParams = new URLSearchParams()

    if (params.creator) searchParams.append('Creator', params.creator)
    if (params.centerNo) searchParams.append('CenterNo', params.centerNo)
    if (params.schoolId) searchParams.append('SchoolId', params.schoolId)
    if (params.examTypeId) searchParams.append('ExamTypeId', params.examTypeId)
    if (params.examYear) searchParams.append('ExamYear', params.examYear.toString())
    if (params.subjectId) searchParams.append('SubjectId', params.subjectId)
    if (params.pageNumber) searchParams.append('PageNumber', params.pageNumber.toString())
    if (params.pageSize) searchParams.append('PageSize', params.pageSize.toString())

    const response = await this.client.get(`/api/ScoreEntry/paginated?${searchParams.toString()}`)
    return response.data
  }

  async getScoreById(id: number): Promise<ScoreEntry> {
    const response: AxiosResponse<ScoreEntry> = await this.client.get(`/api/Scores/${id}`)
    return response.data
  }

  async updateScore(id: number, data: UpdateScoreEntryRequest): Promise<void> {
    await this.client.put(`/api/Scores/${id}`, data)
  }

  async deleteScore(id: number): Promise<void> {
    await this.client.delete(`/api/Scores/${id}`)
  }

  async submitScore(data: CreateScoreEntryRequest): Promise<any> {
    // Convert string studentStatus to API format
    let studentStatusValue: 'Present' | 'Absent' | 'Missing'
    switch (data.studentStatus) {
      case 'Absent':
        studentStatusValue = 'Absent'
        break
      case 'Missing':
        // Map Missing to Missing as the API doesn't have Missing status
        studentStatusValue = 'Missing'
        break
      case 'Present':
      default:
        studentStatusValue = 'Present'
        break
    }

    // Create API request object matching the new /scoreentry endpoint format
    const apiRequest: Partial<ScoreEntryApiDto> = {
      createdBy: data.userId,
      dateCreated: new Date().toISOString(),
      status: 'Unapproved',
      isDeleted: false,
      scoreType: (data.scoreType as any) || 'Initial',
      score: data.score ?? 0, // Convert null to 0 for absent/missing students
      correctionNote: data.correctionNote || '',
      verifiedByUserId: null,
      verifiedAt: null,
      approvedByUserId: null,
      approvedAt: null,
      finalApprovedByUserId: null,
      finalApprovedAt: null,
      paperId: data.paperId,
      examinationNumber: data.examinationNumber,
      candidateId: data.candidateId, // Required - Candidate ID from request
      award: '',
      studentStatus: studentStatusValue,
      // Additional required parameters for querying back
      centerNo: data.centerNo,
      schoolId: data.schoolId,
      examYear: data.examYear
    }

    // Log the API request in development
    if (import.meta.env.DEV) {
      console.log('Submitting score entry to /scoreentry endpoint:', apiRequest)
    }

    const response = await this.client.post('/api/scoreentry', apiRequest)
    return response.data
  }

  async approveScore(data: ScoreApprovalRequest): Promise<void> {
    const params = new URLSearchParams({
      scriptId: data.scriptId.toString(),
      userId: data.userId,
      level: data.level
    })
    await this.client.post(`/api/Scores/approve?${params.toString()}`)
  }

  async verifyScore(data: ScoreVerificationRequest): Promise<void> {
    const scoreEntryDto: ScoreEntryDto = {
      scriptId: data.scriptId,
      userId: data.userId,
      score: data.score,
      scoreType: data.scoreType || 'verify',
      correctionNote: data.correctionNote || '',
      timestamp: new Date(),
      status: 'verified', // Status for verification
      verifiedByUserId: data.userId, // User performing verification
      enteredByUserId: data.userId, // User entering the score
      verifiedAt: new Date(), // Verification timestamp
      approvedByUserId: undefined, // Initially null/undefined
      approvedAt: undefined, // Initially null/undefined
      finalApprovedByUserId: undefined, // Initially null/undefined
      finalApprovedAt: undefined // Initially null/undefined
    }
    await this.client.post('/api/Scores/verify', scoreEntryDto)
  }

  // Batch operations for efficiency
  async submitMultipleScores(scores: CreateScoreEntryRequest[]): Promise<void> {
    const promises = scores.map(score => this.submitScore(score))
    await Promise.all(promises)
  }

  async verifyMultipleScores(scores: ScoreVerificationRequest[]): Promise<void> {
    const promises = scores.map(score => this.verifyScore(score))
    await Promise.all(promises)
  }

  // Utility methods
  async healthCheck(): Promise<boolean> {
    try {
      await this.client.get('/api/health')
      return true
    } catch {
      return false
    }
  }

  // Validate environment configuration
  validateConfiguration(): { isValid: boolean; issues: string[] } {
    const issues: string[] = []

    if (!import.meta.env.VITE_GRADING_API_BASE_URL) {
      issues.push('VITE_GRADING_API_BASE_URL not set in environment variables')
    }

    if (!this.baseURL.startsWith('http')) {
      issues.push('Invalid base URL format - must start with http:// or https://')
    }

    return {
      isValid: issues.length === 0,
      issues
    }
  }

  /**
   * Get grading API specific configuration info
   */
  getGradingApiConfig(): { configSource: string; fallbackUsed: boolean } {
    return {
      configSource: import.meta.env.VITE_GRADING_API_BASE_URL ? 'environment' : 'fallback',
      fallbackUsed: !import.meta.env.VITE_GRADING_API_BASE_URL
    }
  }

  // Helper method to get complete ScoreEntryDto structure example
  getScoreEntryDtoExample(): ScoreEntryDto {
    return {
      scriptId: 240001,
      userId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      score: 85.5,
      scoreType: "first",
      correctionNote: "Example correction note",
      timestamp: new Date(),
      status: "pending",
      verifiedByUserId: undefined,
      enteredByUserId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      verifiedAt: undefined,
      approvedByUserId: undefined,
      approvedAt: undefined,
      finalApprovedByUserId: undefined,
      finalApprovedAt: undefined
    }
  }
}

// Create singleton instance
export const gradingApiService = new GradingApiService()

// Export default
export default gradingApiService
