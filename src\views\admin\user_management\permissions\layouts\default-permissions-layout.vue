<script setup lang="ts">
import BreadcrumbsActions from '@/components/UI/breadcrumbs/breadcrumbs_actions.vue'

const permissionBreadcrumbs = [
  { name: 'Admin', href: '/admin', current: false },
  { name: 'User Management', href: '/admin/user-management', current: false },
  { name: 'Permissions', href: '/admin/user-management/permissions', current: true },
];
import DefaultTable from '@/components/UI/tables/default-table.vue';
import Footer from '@/components/UI/footers/footer-one.vue';
import type { TableColumn, TableRowData } from '@/interfaces';

const permissionColumns: TableColumn[] = [
  { key: 'permission_id', label: 'Permission ID', sortable: true },
  { key: 'name', label: 'Permission Name', sortable: true },
  { key: 'description', label: 'Description', sortable: true },
  { key: 'resource', label: 'Resource', sortable: true },
];

const permissionData: TableRowData[] = [
  { permission_id: 'PERM001', name: 'Create User', description: 'Allows creating new user accounts', resource: 'User' },
  { permission_id: 'PERM002', name: 'Edit User', description: 'Allows editing existing user accounts', resource: 'User' },
  { permission_id: 'PERM003', name: 'Delete User', description: 'Allows deleting user accounts', resource: 'User' },
  { permission_id: 'PERM004', name: 'View Report', description: 'Allows viewing reports', resource: 'Report' },
];

const permissionFilterOptions = [
  { label: 'Last 7 days', value: 'last7days' },
  { label: 'Last 30 days', value: 'last30days' },
  { label: 'All time', value: 'alltime' },
];

const permissionRadioFilters = [
  { label: 'All Permissions', value: 'All' },
  { label: 'User Permissions', value: 'User' },
  { label: 'Report Permissions', value: 'Report' },
];

</script>

<template>
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <BreadcrumbsActions :breadcrumbs="permissionBreadcrumbs" />
        <DefaultTable
          :columns="permissionColumns"
          :data="permissionData"
          title="Permissions"
          :filterOptions="permissionFilterOptions"
          :radioFilters="permissionRadioFilters"
          addButtonLabel="Add New Permission"
          :showAddButton="true"
        />
        <Footer />
      </div>
</template>
