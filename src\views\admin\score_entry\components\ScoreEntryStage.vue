<template>
  <div>
    <!-- Enhanced Search Section -->
    <div v-if="selectedSubject && selectedPaper" class="bg-white rounded-lg shadow-sm p-4">
      <div class="mb-3">
        <h3 class="text-sm font-medium text-gray-900 mb-1">Search Candidates</h3>
        <p class="text-xs text-gray-600">Search by exam number, candidate name, or center number</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Exam Number Search -->
        <div class="relative">
          <label for="examNumber" class="block text-xs font-medium text-gray-700 mb-1">
            Exam Number
          </label>
          <input
            type="text"
            id="examNumber"
            v-model="examNumberSearch"
            @input="onExamNumberSearchInput"
            @focus="showExamNumberDropdown = true"
            @blur="handleExamNumberBlur"
            @keydown="handleExamNumberKeydown"
            placeholder="Search exam number..."
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2 pr-8"
            autocomplete="off"
          />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3 top-6">
            <button
              v-if="examNumberSearch"
              @click="clearExamNumberSearch"
              type="button"
              class="mr-1 text-gray-400 hover:text-gray-600 focus:outline-none"
            >
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
            <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>

          <!-- Exam Number Dropdown -->
          <div
            v-if="showExamNumberDropdown && filteredExamNumbers.length > 0"
            class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg dropdown-list"
          >
            <ul class="py-1 max-h-48 overflow-y-auto">
              <li
                v-for="(candidate, index) in filteredExamNumbers.slice(0, 10)"
                :key="candidate.id"
                @mousedown="selectExamNumber(candidate)"
                @mouseenter="highlightedExamNumberIndex = index"
                :class="[
                  'px-3 py-2 cursor-pointer text-sm transition-colors duration-150',
                  index === highlightedExamNumberIndex ? 'dropdown-item-highlighted' : 'text-gray-900 hover:bg-gray-100'
                ]"
              >
                <div class="font-medium">{{ candidate.examNumber }}</div>
                <div :class="[
                  'text-xs',
                  index === highlightedExamNumberIndex ? 'text-white text-opacity-80' : 'text-gray-500'
                ]">{{ candidate.fullName }}</div>
              </li>
            </ul>
          </div>
        </div>

        <!-- Candidate Name Search -->
        <div class="relative">
          <label for="candidateName" class="block text-xs font-medium text-gray-700 mb-1">
            Candidate Name
          </label>
          <input
            type="text"
            id="candidateName"
            v-model="candidateNameSearch"
            @input="onCandidateNameSearchInput"
            @focus="showCandidateNameDropdown = true"
            @blur="handleCandidateNameBlur"
            @keydown="handleCandidateNameKeydown"
            placeholder="Search candidate name..."
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2 pr-8"
            autocomplete="off"
          />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3 top-6">
            <button
              v-if="candidateNameSearch"
              @click="clearCandidateNameSearch"
              type="button"
              class="mr-1 text-gray-400 hover:text-gray-600 focus:outline-none"
            >
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
            <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>

          <!-- Candidate Name Dropdown -->
          <div
            v-if="showCandidateNameDropdown && filteredCandidateNames.length > 0"
            class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg dropdown-list"
          >
            <ul class="py-1 max-h-48 overflow-y-auto">
              <li
                v-for="(candidate, index) in filteredCandidateNames.slice(0, 10)"
                :key="candidate.id"
                @mousedown="selectCandidateName(candidate)"
                @mouseenter="highlightedCandidateNameIndex = index"
                :class="[
                  'px-3 py-2 cursor-pointer text-sm transition-colors duration-150',
                  index === highlightedCandidateNameIndex ? 'dropdown-item-highlighted' : 'text-gray-900 hover:bg-gray-100'
                ]"
              >
                <div class="font-medium">{{ candidate.fullName }}</div>
                <div :class="[
                  'text-xs',
                  index === highlightedCandidateNameIndex ? 'text-white text-opacity-80' : 'text-gray-500'
                ]">{{ candidate.examNumber }}</div>
              </li>
            </ul>
          </div>
        </div>

        <!-- Center Number Search -->
        <div class="relative">
          <label for="centerNumber" class="block text-xs font-medium text-gray-700 mb-1">
            Center Number
          </label>
          <input
            type="text"
            id="centerNumber"
            v-model="centerNumberSearch"
            @input="onCenterNumberSearchInput"
            @focus="showCenterNumberDropdown = true"
            @blur="handleCenterNumberBlur"
            @keydown="handleCenterNumberKeydown"
            placeholder="Search center number..."
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2 pr-8"
            autocomplete="off"
          />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3 top-6">
            <button
              v-if="centerNumberSearch"
              @click="clearCenterNumberSearch"
              type="button"
              class="mr-1 text-gray-400 hover:text-gray-600 focus:outline-none"
            >
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
            <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>

          <!-- Center Number Dropdown -->
          <div
            v-if="showCenterNumberDropdown && filteredCenterNumbers.length > 0"
            class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg dropdown-list"
          >
            <ul class="py-1 max-h-48 overflow-y-auto">
              <li
                v-for="(center, index) in filteredCenterNumbers.slice(0, 10)"
                :key="center.id"
                @mousedown="selectCenterNumber(center)"
                @mouseenter="highlightedCenterNumberIndex = index"
                :class="[
                  'px-3 py-2 cursor-pointer text-sm transition-colors duration-150',
                  index === highlightedCenterNumberIndex ? 'dropdown-item-highlighted' : 'text-gray-900 hover:bg-gray-100'
                ]"
              >
                <div class="font-medium">Center {{ center.centerNumber }}</div>
                <div :class="[
                  'text-xs',
                  index === highlightedCenterNumberIndex ? 'text-white text-opacity-80' : 'text-gray-500'
                ]">{{ center.centerName }}</div>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Search Results Summary -->
      <div v-if="hasActiveSearch" class="mt-3 p-2 bg-blue-50 rounded-lg border border-blue-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-sm text-blue-800">
              {{ filteredStudentsCount }} candidate(s) found
            </span>
          </div>
          <button
            @click="clearAllSearches"
            class="text-xs text-blue-600 hover:text-blue-800 focus:outline-none"
          >
            Clear all searches
          </button>
        </div>
      </div>
    </div>

    <!-- Score Entry Table -->
    <div v-if="studentsLoaded" class="bg-white rounded-lg shadow-sm">
      <!-- Compact Sorting Controls -->
      <div class="px-4 py-2 mt-2 border-b border-gray-200 bg-red-50">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <!-- Start from Sequence Number - Far Left -->
            <div class="flex items-center space-x-2">
              <div class="flex items-center justify-center w-6 h-6 bg-red-100 rounded">
                <svg class="w-3 h-3 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
              </div>
              <input
                id="startSequence"
                v-model="startFromSequenceNo"
                type="number"
                min="1"
                :max="totalCandidatesLoaded"
                placeholder="Sequence #"
                @keyup.enter="jumpToSequenceNumber"
                @blur="jumpToSequenceNumber"
                class="bg-white border border-gray-300 text-gray-900 text-xs rounded focus:ring-1 focus:ring-maneb-primary focus:border-maneb-primary block w-30 p-1.5 shadow-sm hover:border-gray-400 transition-colors duration-200"
              >
              <button
                @click="jumpToSequenceNumber"
                :disabled="!startFromSequenceNo || startFromSequenceNo < 1 || startFromSequenceNo > totalCandidatesLoaded"
                class="px-2 py-1.5 text-xs font-medium text-white bg-maneb-primary rounded hover:bg-red-700 focus:outline-none focus:ring-1 focus:ring-maneb-primary disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200"
              >
                Go
              </button>
            </div>
          </div>

          <!-- Progress Indicator - Far Right -->
          <div class="flex items-center space-x-1 bg-white px-2 py-1 rounded border border-gray-200 shadow-sm">
            <div class="flex items-center justify-center w-5 h-5 bg-maneb-primary rounded-full">
              <span class="text-xs font-bold text-white">{{ getCurrentSequenceNumber() }}</span>
            </div>
            <span class="text-xs text-gray-600">of</span>
            <span class="text-xs font-semibold text-gray-900">{{ totalCandidatesLoaded }}</span>
          </div>
        </div>
      </div>

      <!-- School Confirmation and Score Progress Section -->
      <div v-if="schoolConfirmationText || totalCandidatesLoaded > 0" class="px-4 py-2 bg-blue-50 border-b border-blue-200">
        <div class="flex items-center justify-between">
          <!-- School Confirmation -->
          <div v-if="schoolConfirmationText" class="flex items-center space-x-2">
            <div class="flex items-center justify-center w-5 h-5 bg-blue-100 rounded-full">
              <svg class="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
            </div>
            <span class="text-sm font-medium text-blue-800">{{ schoolConfirmationText }}</span>
          </div>

          <!-- Score Progress Display -->
          <div v-if="totalCandidatesLoaded > 0" class="flex items-center space-x-4">
            <!-- Scored Count -->
            <div class="flex items-center space-x-1">
              <div class="flex items-center justify-center w-5 h-5 bg-green-100 rounded-full">
                <svg class="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <span class="text-xs font-medium text-green-700">{{ candidatesScored }} Scored</span>
            </div>

            <!-- Remaining Count -->
            <div class="flex items-center space-x-1">
              <div class="flex items-center justify-center w-5 h-5 bg-orange-100 rounded-full">
                <svg class="w-3 h-3 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <span class="text-xs font-medium text-orange-700">{{ candidatesRemaining }} Remaining</span>
            </div>

            <!-- Total Count -->
            <div class="flex items-center space-x-1">
              <div class="flex items-center justify-center w-5 h-5 bg-gray-100 rounded-full">
                <svg class="w-3 h-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </div>
              <span class="text-xs font-medium text-gray-700">{{ totalCandidatesLoaded }} Total</span>
            </div>
          </div>
        </div>
      </div>

      <div class="px-4 py-2 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <p class="text-xs text-blue-600">
              <span class="font-medium">💡</span> Press <kbd class="px-1 py-0.5 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">Enter</kbd> to submit
            </p>
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full text-left text-sm text-gray-500">
          <thead class="bg-gray-50 text-xs uppercase text-gray-700">
            <tr>
              <th scope="col" class="px-4 py-2" title="Unique exam number assigned to each student for this exam session">Exam Number</th>
              <th scope="col" class="px-4 py-2 hidden md:table-cell">Subject</th>
              <th scope="col" class="px-4 py-2 hidden lg:table-cell">Paper</th>
              <th scope="col" class="px-4 py-2">Score</th>
              <th scope="col" class="px-4 py-2">Actions</th>
            </tr>
          </thead>
          <tbody>
            <!-- Loading State -->
            <tr v-if="isLoading">
              <td colspan="6" class="px-4 py-6 text-center">
                <div class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-maneb-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="text-sm text-gray-600">Loading...</span>
                </div>
              </td>
            </tr>

            <!-- No Students Found -->
            <tr v-else-if="filteredStudents.length === 0 && studentSearchQuery">
              <td colspan="6" class="px-4 py-6 text-center">
                <div class="flex flex-col items-center">
                  <svg class="w-8 h-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                  <h3 class="text-sm font-medium text-gray-900 mb-1">No students found</h3>
                  <p class="text-xs text-gray-500 mb-2">No matches for "{{ studentSearchQuery }}"</p>
                  <button
                    @click="clearStudentSearch"
                    class="inline-flex items-center px-3 py-1 text-xs font-medium text-maneb-primary bg-white border border-maneb-primary rounded hover:bg-maneb-primary hover:text-white focus:ring-2 focus:outline-none focus:ring-maneb-primary/50 transition-colors duration-200"
                  >
                    Clear search
                  </button>
                </div>
              </td>
            </tr>

            <!-- All Students Completed -->
            <tr v-else-if="filteredStudents.length === 0 && totalStudentsForEntry === 0">
              <td colspan="6" class="px-6 py-12 text-center">
                <div class="flex flex-col items-center">
                  <div class="flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <h3 class="text-lg font-medium text-gray-900 mb-2">All Scores Entered!</h3>
                  <p class="text-gray-500 mb-4">You have successfully entered scores for all students in this session.</p>
                  <button
                    @click="emit('edit-filters')"
                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-maneb-primary rounded-lg hover:bg-red-700 focus:ring-4 focus:outline-none focus:ring-maneb-primary/50 transition-colors duration-200"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Start New Session
                  </button>
                </div>
              </td>
            </tr>

            <!-- Student Rows -->
            <tr v-else v-for="student in filteredStudents" :key="student.id" class="border-b hover:bg-gray-50">
              <td class="px-4 py-2 text-black">{{ student.examNumber }}</td>
              <td class="px-4 py-2 text-black hidden md:table-cell">{{ student.subjectName }}</td>
              <td class="px-4 py-2 text-black hidden lg:table-cell">{{ student.paperName }}</td>
              <td class="px-4 py-2">
                <div class="flex items-center space-x-2">
                  <input
                    type="text"
                    v-model="student.tempScoreInput"
                    placeholder="Enter score or . / .."
                    :class="[
                      'w-20 px-2 py-1 text-xs border rounded focus:ring-1 focus:ring-maneb-primary focus:border-maneb-primary',
                      isSubmittingToApi ? 'border-blue-300 bg-blue-50 cursor-wait' : 'border-gray-300 bg-white'
                    ]"
                    :disabled="isSubmittingToApi"
                    @input="onScoreInput(student)"
                    @keyup.enter="saveScore(student)"
                    @keyup.escape="resetScore(student)"
                    :data-student-id="student.id"
                  />
                  <span v-if="isSubmittingToApi" class="text-blue-500 text-xs" title="Submitting to grading API...">
                    <svg class="animate-spin h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </span>
                  <span v-else-if="student.hasUnsavedChanges" class="text-orange-500 text-xs" title="Unsaved changes">●</span>
                </div>
              </td>
              <td class="px-4 py-2">
                <div class="flex items-center space-x-1">
                  <!-- View Student - Always visible for users with VIEW permission -->
                  <PermissionGuard
                    :module="PermissionModule.SCORE_ENTRY"
                    :action="PermissionAction.VIEW"
                  >
                    <button
                      @click="viewStudent(student)"
                      class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50 transition-colors duration-200"
                      title="View Student Details"
                    >
                      <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                      </svg>
                    </button>
                  </PermissionGuard>

                  <!-- Edit Notes - Only for users with EDIT permission -->
                  <PermissionGuard
                    :module="PermissionModule.SCORE_ENTRY"
                    :action="PermissionAction.EDIT"
                  >
                    <button
                      @click="openNotesModal(student)"
                      class="text-maneb-primary hover:text-red-700 p-1 rounded hover:bg-red-50 transition-colors duration-200 relative"
                      title="Add/Edit Notes"
                    >
                      <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                      </svg>
                      <span v-if="student.hasNotes" class="absolute -top-1 -right-1 w-1.5 h-1.5 bg-maneb-primary rounded-full"></span>
                    </button>
                  </PermissionGuard>

                  <!-- Delete Score - Only for users with DELETE permission -->
                  <PermissionGuard
                    :module="PermissionModule.SCORE_ENTRY"
                    :action="PermissionAction.DELETE"
                  >
                    <button
                      v-if="student.score !== null"
                      @click="deleteScore(student)"
                      class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50 transition-colors duration-200"
                      title="Delete Score"
                    >
                      <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                      </svg>
                    </button>
                  </PermissionGuard>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Empty State -->
        <div v-if="!isLoading && eligibleStudents.length === 0" class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No students found</h3>
          <p class="mt-1 text-sm text-gray-500">No students are registered for the selected criteria.</p>
        </div>
      </div>
    </div>

    <!-- Completed Scores Table -->
    <div v-if="showCompletedScores && completedScores.length > 0" class="bg-white rounded-lg shadow-sm mt-6">
      <div class="px-4 py-3 border-b border-gray-200 bg-green-50">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <div class="flex items-center justify-center w-6 h-6 bg-green-100 rounded">
              <svg class="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <h4 class="text-sm font-semibold text-gray-900">Completed Scores</h4>
          </div>
          <div class="flex items-center space-x-2">
            <div v-if="completedScoresLoading" class="flex items-center space-x-1">
              <svg class="animate-spin h-3 w-3 text-gray-600" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span class="text-xs text-gray-600">Loading...</span>
            </div>
            <div v-else class="text-xs text-gray-600">
              {{ completedScoresPagination.totalCount }} total score{{ completedScoresPagination.totalCount !== 1 ? 's' : '' }}
            </div>
          </div>
        </div>
      </div>

      <!-- Search Bar for Completed Scores -->
      <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <input
            type="text"
            v-model="completedScoresSearchQuery"
            placeholder="Search by exam number, status..."
            class="w-full pl-10 pr-10 py-2 text-sm border border-gray-300 rounded-lg focus:ring-1 focus:ring-maneb-primary focus:border-maneb-primary"
          />
          <button
            v-if="completedScoresSearchQuery"
            @click="clearCompletedScoresSearch"
            class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full text-left text-sm text-gray-500">
          <thead class="bg-gray-50 text-xs uppercase text-gray-700">
            <tr>
              <th scope="col" class="px-4 py-2">Exam Number</th>
              <th scope="col" class="px-4 py-2">Score</th>
              <th scope="col" class="px-4 py-2">Student Status</th>
              <th scope="col" class="px-4 py-2 hidden sm:table-cell">Score Type</th>
              <th scope="col" class="px-4 py-2 hidden sm:table-cell">API Status</th>
              <th scope="col" class="px-4 py-2 hidden lg:table-cell">Time Created</th>
            </tr>
          </thead>
          <tbody>
            <!-- Loading State -->
            <tr v-if="completedScoresLoading">
              <td :colspan="6" class="px-4 py-8 text-center">
                <div class="flex items-center justify-center space-x-2">
                  <svg class="animate-spin h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="text-sm text-gray-600">Loading completed scores...</span>
                </div>
              </td>
            </tr>

            <!-- Empty State -->
            <tr v-else-if="completedScores.length === 0">
              <td :colspan="6" class="px-4 py-8 text-center">
                <div class="text-gray-500">
                  <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  <p class="text-sm font-medium">No completed scores found</p>
                  <p class="text-xs text-gray-400 mt-1">Scores will appear here after they are submitted to the API</p>
                </div>
              </td>
            </tr>

            <!-- No Search Results -->
            <tr v-else-if="filteredCompletedScores.length === 0">
              <td :colspan="6" class="px-4 py-8 text-center">
                <div class="text-gray-500">
                  <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                  <p class="text-sm font-medium">No scores match your search</p>
                  <p class="text-xs text-gray-400 mt-1">Try adjusting your search terms</p>
                </div>
              </td>
            </tr>

            <!-- Score Rows -->
            <tr v-else v-for="score in filteredCompletedScores" :key="score.id" class="border-b hover:bg-gray-50">
              <td class="px-4 py-2 font-medium text-gray-900">{{ score.examinationNumber }}</td>
              <td class="px-4 py-2">
                <span v-if="score.score !== null" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  {{ score.score }}%
                </span>
                <span v-else class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  No Score
                </span>
              </td>
              <td class="px-4 py-2">
                <span :class="[
                  'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                  score.studentStatus === 'Absent' ? 'bg-red-100 text-red-800' :
                  score.studentStatus === 'Missing' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-blue-100 text-blue-800'
                ]">
                  {{ score.studentStatus }}
                </span>
              </td>
              <td class="px-4 py-2 text-black hidden sm:table-cell">{{ score.scoreType }}</td>
              <td class="px-4 py-2 text-black hidden sm:table-cell">{{ score.status }}</td>
              <td class="px-4 py-2 text-gray-500 hidden lg:table-cell">
                {{ new Date(score.dateCreated).toLocaleTimeString() }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Enhanced Pagination Controls -->
      <div class="px-4 py-3 border-t border-gray-200 bg-gray-50">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-3 sm:space-y-0">
          <!-- Results Info and Page Size Selector -->
          <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
            <div class="text-sm text-gray-700">
              Showing {{ ((completedScoresPagination.pageNumber - 1) * completedScoresPagination.pageSize) + 1 }} to
              {{ Math.min(completedScoresPagination.pageNumber * completedScoresPagination.pageSize, completedScoresPagination.totalCount) }}
              of {{ completedScoresPagination.totalCount }} results
            </div>

            <!-- Page Size Selector -->
            <div class="flex items-center space-x-2">
              <label for="pageSize" class="text-sm text-gray-700">Show:</label>
              <select
                id="pageSize"
                v-model="completedScoresPagination.pageSize"
                @change="onPageSizeChange"
                :disabled="completedScoresLoading"
                class="text-sm border border-gray-300 rounded-md px-2 py-1 focus:ring-1 focus:ring-maneb-primary focus:border-maneb-primary disabled:bg-gray-100 disabled:cursor-not-allowed"
              >
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </select>
              <span class="text-sm text-gray-700">per page</span>
            </div>

            <!-- Quick Jump to Page (for large datasets) -->
            <div v-if="completedScoresPagination.totalPages > 10" class="flex items-center space-x-2">
              <label for="jumpToPage" class="text-sm text-gray-700">Go to page:</label>
              <input
                id="jumpToPage"
                type="number"
                :min="1"
                :max="completedScoresPagination.totalPages"
                v-model.number="jumpToPageNumber"
                @keydown.enter="jumpToPage"
                :disabled="completedScoresLoading"
                class="w-16 text-sm border border-gray-300 rounded-md px-2 py-1 focus:ring-1 focus:ring-maneb-primary focus:border-maneb-primary disabled:bg-gray-100 disabled:cursor-not-allowed"
                :placeholder="completedScoresPagination.pageNumber.toString()"
              />
              <button
                @click="jumpToPage"
                :disabled="completedScoresLoading || !jumpToPageNumber || jumpToPageNumber < 1 || jumpToPageNumber > completedScoresPagination.totalPages"
                class="px-2 py-1 text-sm bg-maneb-primary text-white rounded-md hover:bg-maneb-primary-dark focus:outline-none focus:ring-1 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-maneb-primary"
              >
                Go
              </button>
            </div>
          </div>

          <!-- Pagination Navigation -->
          <div v-if="completedScoresPagination.totalPages > 1" class="flex items-center justify-center sm:justify-end space-x-1">
            <!-- Previous Button -->
            <button
              @click="loadCompletedScores(completedScoresPagination.pageNumber - 1)"
              :disabled="!completedScoresPagination.hasPreviousPage || completedScoresLoading"
              class="relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-maneb-primary focus:border-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white"
            >
              <span class="sr-only">Previous</span>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </button>

            <!-- Page Numbers -->
            <template v-for="page in getVisiblePages()" :key="page">
              <button
                v-if="page !== '...'"
                @click="loadCompletedScores(Number(page))"
                :disabled="completedScoresLoading"
                :class="[
                  'relative inline-flex items-center px-4 py-2 text-sm font-medium border focus:z-10 focus:outline-none focus:ring-1 focus:ring-maneb-primary focus:border-maneb-primary disabled:cursor-not-allowed',
                  page === completedScoresPagination.pageNumber
                    ? 'z-10 bg-maneb-primary border-maneb-primary text-white'
                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50 disabled:hover:bg-white'
                ]"
              >
                {{ page }}
              </button>
              <span
                v-else
                class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300"
              >
                ...
              </span>
            </template>

            <!-- Next Button -->
            <button
              @click="loadCompletedScores(completedScoresPagination.pageNumber + 1)"
              :disabled="!completedScoresPagination.hasNextPage || completedScoresLoading"
              class="relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-maneb-primary focus:border-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white"
            >
              <span class="sr-only">Next</span>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Initial State - No Exam Number Selected -->
    <div v-else-if="!studentsLoaded" class="bg-white rounded-lg shadow-sm p-12 text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <h3 class="mt-2 text-lg font-medium text-gray-900">Select Exam Number</h3>
      <p class="mt-1 text-sm text-gray-500">Choose an exam number above to load eligible students for score entry.</p>
    </div>



    <!-- Student Notes Modal -->
    <div
      v-if="isNotesModalOpen"
      class="fixed top-0 left-0 right-0 z-50 w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full bg-gray-900 bg-opacity-50 flex items-center justify-center"
      @click.self="closeNotesModal"
    >
      <div class="relative w-full max-w-2xl max-h-full">
        <div class="relative bg-white rounded-lg shadow">
          <!-- Modal header -->
          <div class="flex items-start justify-between p-4 border-b rounded-t">
            <h3 class="text-xl font-semibold text-gray-900">
              Student Notes - {{ selectedStudentForNotes?.fullName }}
            </h3>
            <button
              @click="closeNotesModal"
              type="button"
              class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center"
            >
              <svg class="w-3 h-3" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
              </svg>
            </button>
          </div>

          <!-- Modal body -->
          <div class="p-6 space-y-4">
            <!-- Student Info -->
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-gray-600">Student ID:</span>
                  <span class="ml-2 font-medium text-black">{{ selectedStudentForNotes?.studentId }}</span>
                </div>
                <div>
                  <span class="text-gray-600">Exam Number:</span>
                  <span class="ml-2 font-medium text-black">{{ selectedStudentForNotes?.examNumber }}</span>
                </div>
                <div>
                  <span class="text-gray-600">Subject:</span>
                  <span class="ml-2 font-medium text-black">{{ selectedStudentForNotes?.subjectName }}</span>
                </div>
                <div>
                  <span class="text-gray-600">Current Score:</span>
                  <span class="ml-2 font-medium" :class="getScoreColor(selectedStudentForNotes?.score ?? null)">
                    {{ selectedStudentForNotes?.score !== null ? selectedStudentForNotes?.score + '%' : 'Not entered' }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Notes Input -->
            <div>
              <label for="studentNotes" class="block mb-2 text-sm font-medium text-gray-900">
                Notes
              </label>
              <textarea
                id="studentNotes"
                v-model="currentNotes"
                rows="6"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5"
                placeholder="Enter notes about this student's performance, special circumstances, or other relevant information..."
              ></textarea>
              <p class="mt-1 text-xs text-gray-500">These notes will be saved with the student's record and can be viewed by authorized personnel.</p>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-end space-x-3 pt-4 border-t">
              <button
                @click="closeNotesModal"
                type="button"
                class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900"
              >
                Cancel
              </button>
              <button
                @click="saveNotes"
                type="button"
                :disabled="isSavingNotes"
                class="text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:ring-4 focus:outline-none focus:ring-maneb-primary/50 font-medium rounded-lg text-sm px-5 py-2.5 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                <span v-if="isSavingNotes" class="flex items-center">
                  <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </span>
                <span v-else>Save Notes</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import PermissionGuard from '@/components/shared/auth/PermissionGuard.vue'
import sweetAlert from '@/utils/ui/sweetAlert'
import { useDebouncedCandidateSearch } from '@/composables/data/useCandidates'
import { useGradingApiStore, useAuthStore, useCandidateStore } from '@/stores'
import type { ScoreType, CreateScoreEntryRequest } from '@/interfaces/external/results-api.interface'
import type { ScoreCandidateDto } from '@/services/data/candidate.service'
import { PermissionModule, PermissionAction } from '@/utils/auth/permissions'
import { useScoreEntryPermissions } from '@/composables/auth/usePermissions'


// Props
interface Props {
  selectedFilters: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'edit-filters': []
  'score-updated': [data: any]
}>()

// Stores
const gradingApiStore = useGradingApiStore()
const authStore = useAuthStore()
const candidateStore = useCandidateStore()

// Types
interface Student {
  id: string
  studentId: string
  examNumber: string
  fullName: string
  subjectName: string
  paperName: string
  score: number | null
  tempScore: number | null
  tempScoreInput: string // New field for text input (handles dots and numbers)
  studentStatus?: 'Present' | 'Absent' | 'Missing' // Student attendance status
  hasUnsavedChanges: boolean
  notes: string
  hasNotes: boolean
  // Additional fields from candidate data
  sequenceNo?: number
  firstName?: string
  surname?: string
  districtName?: string
  districtId?: string
  school?: string
  schoolId?: string
  centreNo?: string
  cohortName?: string
  examYear?: number
  examTypeId?: string
  cohortId?: string
  firstname?: string
  dateOfBirth?: string
  gender?: string
  registrationDate?: string
}

interface CompletedScore {
  id: string
  examinationNumber: string
  paperId: string
  score: number | null
  scoreType: string
  studentStatus: string
  status: string
  dateCreated: string
  createdBy: string
  correctionNote: string
}

// Reactive state
const eligibleStudents = ref<Student[]>([])
const candidates = ref<ScoreCandidateDto[]>([])
const candidateCount = ref(0)
const isLoading = ref(false)
const isSaving = ref(false)
const isLoadingCandidatesFromSummary = ref(false)

const isNotesModalOpen = ref(false)
const selectedStudentForNotes = ref<Student | null>(null)
const currentNotes = ref('')
const isSavingNotes = ref(false)
const selectedExamNumber = ref('')
const studentsLoaded = ref(false)

// Completed scores state
const completedScores = ref<CompletedScore[]>([])
const showCompletedScores = ref(false)
const completedScoresSearchQuery = ref('')
const completedScoresLoading = ref(false)

// Enhanced pagination state with persistence
const completedScoresPagination = ref({
  pageNumber: 1,
  pageSize: parseInt(localStorage.getItem('maneb-completed-scores-page-size') || '25'),
  totalCount: 0,
  totalPages: 0,
  hasNextPage: false,
  hasPreviousPage: false
})

// Jump to page functionality
const jumpToPageNumber = ref<number | null>(null)

// Sequence number navigation
const startFromSequenceNo = ref<number | null>(null)

// School confirmation state
const loadedSchoolName = ref<string>('')
const loadedCenterNumber = ref<string>('')
const loadedDistrictName = ref<string>('')

// Enhanced Search State
const examNumberSearch = ref('')
const candidateNameSearch = ref('')
const centerNumberSearch = ref('')

// Dropdown states
const showExamNumberDropdown = ref(false)
const showCandidateNameDropdown = ref(false)
const showCenterNumberDropdown = ref(false)

// Highlighted indices for keyboard navigation
const highlightedExamNumberIndex = ref(-1)
const highlightedCandidateNameIndex = ref(-1)
const highlightedCenterNumberIndex = ref(-1)

const selectedExamNumberData = ref<any>(null)

// Load candidates from API using composable
const {
  candidateOptions: availableExamNumbers,
  debouncedSearch,
  isSearching: isLoadingCandidates,
  setFilters: setCandidateFilters
} = useDebouncedCandidateSearch(300)
const studentSearchQuery = ref('')
const debouncedSearchQuery = ref('')
const searchTimeout = ref<ReturnType<typeof setTimeout> | null>(null)

// Sequential entry controls
const currentStudentIndex = ref(0)

// Score entry configuration
const selectedScoreType = ref<ScoreType>('Initial' as ScoreType)
const isSubmittingToApi = ref(false)

// Subject and Paper selection state
const selectedSubject = ref('')
const selectedPaper = ref('')
const availableSubjects = ref<Array<{id: string, name: string}>>([])
const availablePapers = ref<Array<{id: string, name: string}>>([])
const isLoadingSubjects = ref(false)
const isLoadingPapers = ref(false)

// Toast notification utility
const showToast = (message: string, type: 'success' | 'warning' | 'error' = 'success') => {
  // Create toast element
  const toast = document.createElement('div')
  toast.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg text-white text-sm font-medium transition-all duration-300 transform translate-x-full opacity-0 ${
    type === 'success' ? 'bg-green-500' :
    type === 'warning' ? 'bg-yellow-500' :
    'bg-red-500'
  }`
  toast.textContent = message

  // Add to DOM
  document.body.appendChild(toast)

  // Animate in
  setTimeout(() => {
    toast.classList.remove('translate-x-full', 'opacity-0')
  }, 100)

  // Auto remove after 3 seconds
  setTimeout(() => {
    toast.classList.add('translate-x-full', 'opacity-0')
    setTimeout(() => {
      if (document.body.contains(toast)) {
        document.body.removeChild(toast)
      }
    }, 300)
  }, 3000)
}



// Load candidates from Score Candidates endpoint
const loadCandidatesFromSummary = async () => {
  if (!props.selectedFilters) {
    console.warn('No filters provided for candidate loading')
    return
  }

  isLoadingCandidatesFromSummary.value = true
  try {
    console.log('🔍 Loading candidates from ScoreCandidates endpoint with filters:', props.selectedFilters)

    // Get the center number from the selected filters
    // Try multiple possible property names for center number
    let centerNumber = props.selectedFilters.centerNumber ||
                      props.selectedFilters.manebcentreNo ||
                      props.selectedFilters.centerNo

    const schoolId = props.selectedFilters.schoolId
    const examTypeId = props.selectedFilters.examTypeId

    // If we don't have a center number but we have a center ID, try to extract it
    if (!centerNumber && props.selectedFilters.center) {
      console.log('🔍 Attempting to extract center number from center ID:', props.selectedFilters.center)
      // The center ID might contain the center number, or we might need to look it up
      // For now, let's try using the center ID directly as a fallback
      centerNumber = props.selectedFilters.center
    }

    console.log('🔍 Center detection:', {
      centerNumber,
      centerNumberProp: props.selectedFilters.centerNumber,
      manebcentreNoProp: props.selectedFilters.manebcentreNo,
      centerNoProp: props.selectedFilters.centerNo,
      centerId: props.selectedFilters.center,
      schoolId,
      examTypeId,
      allFilterKeys: Object.keys(props.selectedFilters)
    })

    if (!centerNumber) {
      // For PLSCE, we might need to handle the case where center is not yet selected
      if (!props.selectedFilters.center) {
        candidateCount.value = 0
        candidates.value = []
        eligibleStudents.value = []
        studentsLoaded.value = false
        return
      }

      throw new Error('Center number is required but not found in filters. Please ensure a center is selected.')
    }

    const filters = {
      schoolId: schoolId, // Add school ID for API calls
      centerNo: centerNumber, // Use the actual center number, not the center ID
      examYear: props.selectedFilters.examYear || new Date().getFullYear(),
      subjectId: props.selectedFilters.subject,
      examTypeId: examTypeId // Add exam type ID for API calls
      // Client-side sorting will be handled in computed properties
    }

    console.log('🔍 API call filters:', filters)
    const response = await candidateStore.fetchScoreCandidates(filters)
    candidates.value = response
    candidateCount.value = response.length

    console.log('✅ Candidates loaded from ScoreCandidates endpoint:', response.length, 'candidates')
    console.log(' Sample candidates:', response.slice(0, 3))

    // Extract school information for confirmation display
    if (response.length > 0) {
      const firstCandidate = response[0]
      loadedSchoolName.value = firstCandidate.schoolName || ''
      loadedCenterNumber.value = firstCandidate.manebcentreNo || centerNumber || ''

      // Try to get district name from candidate data or filters
      loadedDistrictName.value = firstCandidate.districtName ||
                                 props.selectedFilters?.districtName ||
                                 getDistrictName() || ''

      console.log('🏫 School information extracted:', {
        schoolName: loadedSchoolName.value,
        centerNumber: loadedCenterNumber.value,
        districtName: loadedDistrictName.value
      })
    }

    // Convert candidates to students for the score entry table using new data structure
    eligibleStudents.value = response.map((candidate: ScoreCandidateDto, index: number) => ({
      id: candidate.id,
      studentId: candidate.examinationNo, // Use examination number as student ID
      examNumber: candidate.examinationNo,
      fullName: `${candidate.firstname} ${candidate.surname}`.trim(),
      subjectName: candidate.subjectName || props.selectedFilters?.subjectName || 'Unknown Subject',
      paperName: props.selectedFilters.paper || 'Unknown Paper',
      score: null,
      tempScore: null,
      tempScoreInput: '', // Initialize empty string for score input
      hasUnsavedChanges: false,
      notes: '',
      hasNotes: false,
      // Additional fields from candidate data
      sequenceNo: candidate.sequenceNo || (index + 1), // Use API sequence number or generate
      firstName: candidate.firstname,
      surname: candidate.surname,
      centreNo: candidate.manebcentreNo,
      examTypeId: candidate.examTypeId,
      firstname: candidate.firstname, // Keep for backward compatibility
      schoolName: candidate.schoolName,
      schoolId: candidate.schoolId,
      examYear: candidate.examYear,
      candidateEntryTypeId: candidate.candidateEntryTypeId
    }))

    studentsLoaded.value = true
    currentStudentIndex.value = 0

    console.log('✅ Students converted for score entry:', eligibleStudents.value.length, 'students')

  } catch (error) {
    console.error('❌ Error loading candidates from CandidateSummary endpoint:', error)
    candidates.value = []
    candidateCount.value = 0
    eligibleStudents.value = []
    studentsLoaded.value = false

    // Clear school information on error
    loadedSchoolName.value = ''
    loadedCenterNumber.value = ''
    loadedDistrictName.value = ''

    sweetAlert.error('Error', 'Failed to load candidates from summary endpoint')
  } finally {
    isLoadingCandidatesFromSummary.value = false
  }
}



// Utility function to get the paper ID from selected filters
const getScriptId = (): string => {
  // Get the paper ID from the selected filters
  const paperId = props.selectedFilters?.paper
  if (!paperId || paperId.trim() === '') {
    console.warn('No paper ID found in selected filters')
    return '0'
  }
  console.log('📄 Using paper ID from filters:', paperId)
  return paperId.toString()
}

// Base student data template - will be populated with real paper codes from API
const baseStudentData = [
  {
    id: '1',
    studentId: 'STU001',
    examNumber: 'EXAM001',
    fullName: 'John Banda',
    subjectName: 'Mathematics',
    paperName: 'Paper 1 (Pure Mathematics)',
    score: null,
    tempScore: 85,
    tempScoreInput: '85',
    studentStatus: 'Present',
    hasUnsavedChanges: false,
    notes: 'Excellent performance in algebra section.',
    hasNotes: true
  },
  {
    id: '2',
    studentId: 'STU002',
    examNumber: 'EXAM002',
    fullName: 'Mary Phiri',
    subjectName: 'Mathematics',
    paperName: 'Paper 1 (Pure Mathematics)',
    score: null,
    tempScore: null,
    tempScoreInput: '',
    studentStatus: 'Present',
    hasUnsavedChanges: false,
    notes: '',
    hasNotes: false
  },
  {
    id: '3',
    studentId: 'STU003',
    examNumber: 'EXAM003',
    fullName: 'Peter Mwale',
    subjectName: 'Mathematics',
    paperName: 'Paper 1 (Pure Mathematics)',
    score: null,
    tempScore: null,
    tempScoreInput: '',
    studentStatus: 'Present',
    hasUnsavedChanges: false,
    notes: 'Struggled with geometry questions but showed good understanding of calculus.',
    hasNotes: true
  },
  {
    id: '4',
    studentId: 'STU004',
    examNumber: 'EXAM004',
    fullName: 'Grace Tembo',
    subjectName: 'Mathematics',
    paperName: 'Paper 2 (Applied Mathematics)',
    score: null,
    tempScore: null,
    tempScoreInput: '',
    studentStatus: 'Present',
    hasUnsavedChanges: false,
    notes: 'Outstanding performance across all sections.',
    hasNotes: true
  },
  {
    id: '5',
    studentId: 'STU005',
    examNumber: 'EXAM005',
    fullName: 'James Zulu',
    subjectName: 'Mathematics',
    paperName: 'Paper 1 (Pure Mathematics)',
    score: null,
    tempScore: null,
    tempScoreInput: '',
    studentStatus: 'Present',
    hasUnsavedChanges: false,
    notes: '',
    hasNotes: false
  },
  {
    id: '6',
    studentId: 'STU006',
    examNumber: 'EXAM006',
    fullName: 'Sarah Banda',
    subjectName: 'English Language',
    paperName: 'Paper 1 (Language)',
    score: null,
    tempScore: null,
    tempScoreInput: '',
    studentStatus: 'Present',
    hasUnsavedChanges: false,
    notes: 'Good essay writing skills.',
    hasNotes: true
  },
  {
    id: '7',
    studentId: 'STU007',
    examNumber: 'EXAM007',
    fullName: 'Michael Phiri',
    subjectName: 'English Language',
    paperName: 'Paper 1 (Language)',
    score: null,
    tempScore: null,
    tempScoreInput: '',
    studentStatus: 'Present',
    hasUnsavedChanges: false,
    notes: '',
    hasNotes: false
  },
  {
    id: '9',
    studentId: 'STU009',
    examNumber: 'EXAM009',
    fullName: 'Grace Mbewe',
    subjectName: 'English Language',
    paperName: 'Paper 1 (Language)',
    score: null,
    tempScore: null,
    tempScoreInput: '',
    studentStatus: 'Present',
    hasUnsavedChanges: false,
    notes: 'Strong vocabulary and grammar skills.',
    hasNotes: true
  },
  {
    id: '10',
    studentId: 'STU010',
    examNumber: 'EXAM010',
    fullName: 'David Chirwa',
    subjectName: 'English Language',
    paperName: 'Paper 1 (Language)',
    score: null,
    tempScore: null,
    tempScoreInput: '',
    studentStatus: 'Present',
    hasUnsavedChanges: false,
    notes: '',
    hasNotes: false
  },
  {
    id: '8',
    studentId: 'STU008',
    examNumber: 'EXAM008',
    fullName: 'Linda Mwale',
    subjectName: 'Physical Science',
    paperName: 'Paper 1 (Theory)',
    score: null,
    tempScore: null,
    tempScoreInput: '',
    studentStatus: 'Present',
    hasUnsavedChanges: false,
    notes: 'Strong in physics, needs improvement in chemistry.',
    hasNotes: true
  }
]

// Dynamic students array that will be populated with real paper codes
let mockStudents: Student[] = []

// Function to fetch papers from API and generate students with real paper codes
const generateStudentsWithPaperCodes = async () => {
  try {
    console.log('🔄 Fetching papers from API...')

    // Initialize grading API service
    gradingApiStore.initializeApiService()

    // Fetch papers from the API
    await gradingApiStore.fetchPapers()
    const apiPapers = gradingApiStore.papers

    console.log('📄 Fetched papers from API:', apiPapers)
    console.log(`📊 Found ${apiPapers.length} papers from API`)

    if (apiPapers.length === 0) {
      console.warn('⚠️ No papers found from API, using fallback mock data')
      // Fallback to mock exam sessions if API returns no data
      generateStudentsFromMockSessions()
      return
    }

    // Clear existing mock students
    mockStudents = []

    // Generate students for each paper
    apiPapers.forEach((paper: any, paperIndex: number) => {
      // Create 2-3 students per paper
      const studentsPerPaper = Math.min(3, baseStudentData.length - paperIndex * 2)

      for (let i = 0; i < studentsPerPaper && (paperIndex * 2 + i) < baseStudentData.length; i++) {
        const baseStudent = baseStudentData[paperIndex * 2 + i]

        const student: Student = {
          ...baseStudent,
          examNumber: baseStudent.examNumber, // Keep the original student examination number
          subjectName: paper.subjectName,
          paperName: `Paper ${paper.id} (${paper.subjectName})`, // Use API data
          id: `${paperIndex * 2 + i + 1}`, // Ensure unique IDs
          tempScoreInput: baseStudent.tempScoreInput || '',
          studentStatus: (baseStudent.studentStatus as 'Present' | 'Absent' | 'Missing') || 'Present'
        }

        mockStudents.push(student)
      }
    })

    console.log('✅ Generated students with API paper codes:', mockStudents)
    console.log(`👥 Total students generated: ${mockStudents.length}`)

  } catch (error) {
    console.error('❌ Error fetching papers from API:', error)
    console.log('🔄 Falling back to mock exam sessions')
    // Fallback to mock exam sessions if API fails
    generateStudentsFromMockSessions()
  }
}

// Fallback function to generate students from mock exam sessions
const generateStudentsFromMockSessions = () => {
  console.log('🔄 Generating students from mock exam sessions...')
  mockStudents = []

  // Map base student data to mock exam sessions
  baseStudentData.forEach((baseStudent) => {
    let subjectName = baseStudent.subjectName
    let paperName = baseStudent.paperName

    // Find matching mock exam session
    const matchingSession = mockExamSessions.find(session =>
      session.subject === baseStudent.subjectName &&
      session.paper === baseStudent.paperName
    )

    if (matchingSession) {
      subjectName = matchingSession.subject
      paperName = matchingSession.paper
    }

    const student: Student = {
      ...baseStudent,
      examNumber: baseStudent.examNumber, // Keep the original student examination number
      subjectName: subjectName,
      paperName: paperName,
      tempScoreInput: baseStudent.tempScoreInput || '',
      studentStatus: (baseStudent.studentStatus as 'Present' | 'Absent' | 'Missing') || 'Present'
    }

    mockStudents.push(student)
  })

  console.log('✅ Generated students with mock paper codes:', mockStudents)
  console.log(`👥 Total students generated from mock: ${mockStudents.length}`)
}

// Computed properties
const hasUnsavedChanges = computed(() => {
  return eligibleStudents.value.some(student => student.hasUnsavedChanges)
})

const completedScoresCount = computed(() => {
  return eligibleStudents.value.filter(student => student.score !== null).length
})

// Computed property for completed scores in reverse chronological order
const sortedCompletedScores = computed(() => {
  return [...completedScores.value].sort((a, b) =>
    new Date(b.dateCreated).getTime() - new Date(a.dateCreated).getTime()
  )
})

// Computed property for filtered and sorted completed scores
// Note: With server-side pagination, we display all scores from current page
// Search filtering is handled by reloading data from server
const filteredCompletedScores = computed(() => {
  const query = completedScoresSearchQuery.value.toLowerCase().trim()

  if (!query) {
    return sortedCompletedScores.value
  }

  // For client-side search within current page results
  return sortedCompletedScores.value.filter(score => {
    return (
      score.examinationNumber?.toLowerCase().includes(query) ||
      score.paperId?.toLowerCase().includes(query) ||
      score.studentStatus?.toLowerCase().includes(query) ||
      score.scoreType?.toLowerCase().includes(query) ||
      score.status?.toLowerCase().includes(query) ||
      score.score?.toString().includes(query)
    )
  })
})

// Computed property for sorted students (for sequential entry)
// Always sort by sequenceNo in ascending order (1, 2, 3, etc.)
const sortedStudents = computed(() => {
  const students = [...eligibleStudents.value]

  return students.sort((a, b) => {
    const aSequence = a.sequenceNo || 0
    const bSequence = b.sequenceNo || 0

    // Always sort in ascending order by sequence number
    return aSequence - bSequence
  })
})

const totalStudentsForEntry = computed(() => {
  return sortedStudents.value.length
})

// Score progress computed properties
const totalCandidatesLoaded = computed(() => {
  return candidates.value.length
})

const candidatesScored = computed(() => {
  // Count candidates that have been removed from eligibleStudents (meaning they were scored)
  return totalCandidatesLoaded.value - eligibleStudents.value.length
})

const candidatesRemaining = computed(() => {
  return eligibleStudents.value.length
})

// School confirmation computed properties
const schoolConfirmationText = computed(() => {
  const parts: string[] = []

  // Add district name if available
  if (loadedDistrictName.value) {
    parts.push(loadedDistrictName.value)
  }

  // Add center and school information
  if (loadedSchoolName.value && loadedCenterNumber.value) {
    parts.push(`Center ${loadedCenterNumber.value}: ${loadedSchoolName.value}`)
  } else if (loadedSchoolName.value) {
    parts.push(`School: ${loadedSchoolName.value}`)
  } else if (loadedCenterNumber.value) {
    parts.push(`Center: ${loadedCenterNumber.value}`)
  }

  return parts.join(' | ')
})

const filteredExamNumbers = computed(() => {
  console.log('🔍 filteredExamNumbers computed - availableExamNumbers.value:', availableExamNumbers.value.length, 'items')
  console.log('🔍 examNumberSearch.value:', examNumberSearch.value)

  if (!examNumberSearch.value) {
    return availableExamNumbers.value
  }

  const searchTerm = examNumberSearch.value.toLowerCase()
  return availableExamNumbers.value.filter((candidate: any) =>
    candidate.examNumber.toLowerCase().includes(searchTerm) ||
    candidate.fullName.toLowerCase().includes(searchTerm) ||
    candidate.firstName.toLowerCase().includes(searchTerm) ||
    candidate.lastName.toLowerCase().includes(searchTerm)
  )
})

const filteredStudents = computed(() => {
  // For sequential entry, return only the current student
  const sorted = sortedStudents.value

  // If there's a search query, filter the sorted students first
  let studentsToShow = sorted
  if (studentSearchQuery.value.trim()) {
    const searchTerm = studentSearchQuery.value.toLowerCase().trim()
    studentsToShow = sorted.filter(student =>
      student.examNumber.toLowerCase().includes(searchTerm) ||
      student.studentId.toLowerCase().includes(searchTerm)
    )
  }

  // Return only the current student for sequential entry
  if (studentsToShow.length > 0 && currentStudentIndex.value < studentsToShow.length) {
    return [studentsToShow[currentStudentIndex.value]]
  }

  return []
})

// New computed properties for enhanced search
const filteredCandidateNames = computed(() => {
  if (!candidateNameSearch.value) {
    return availableExamNumbers.value
  }

  const searchTerm = candidateNameSearch.value.toLowerCase()
  return availableExamNumbers.value.filter((candidate: any) =>
    candidate.fullName.toLowerCase().includes(searchTerm) ||
    candidate.firstName.toLowerCase().includes(searchTerm) ||
    candidate.lastName.toLowerCase().includes(searchTerm)
  )
})

const filteredCenterNumbers = computed(() => {
  if (!centerNumberSearch.value) {
    return mockCenterNumbers.value
  }

  const searchTerm = centerNumberSearch.value.toLowerCase()
  return mockCenterNumbers.value.filter((center: any) =>
    center.centerNumber.toLowerCase().includes(searchTerm) ||
    center.centerName.toLowerCase().includes(searchTerm)
  )
})

const hasActiveSearch = computed(() => {
  return examNumberSearch.value.trim() !== '' ||
         candidateNameSearch.value.trim() !== '' ||
         centerNumberSearch.value.trim() !== ''
})

const filteredStudentsCount = computed(() => {
  let count = eligibleStudents.value.length

  if (hasActiveSearch.value) {
    const examSearchTerm = examNumberSearch.value.toLowerCase().trim()
    const nameSearchTerm = candidateNameSearch.value.toLowerCase().trim()
    const centerSearchTerm = centerNumberSearch.value.toLowerCase().trim()

    count = eligibleStudents.value.filter(student => {
      const matchesExamNumber = !examSearchTerm || student.examNumber.toLowerCase().includes(examSearchTerm)
      const matchesName = !nameSearchTerm || student.fullName.toLowerCase().includes(nameSearchTerm)
      const matchesCenter = !centerSearchTerm || true // Center matching would need additional data

      return matchesExamNumber && matchesName && matchesCenter
    }).length
  }

  return count
})



// Mock center numbers data
const mockCenterNumbers = computed(() => [
  { id: '1', centerNumber: '001', centerName: 'Lilongwe Central Center' },
  { id: '2', centerNumber: '002', centerName: 'Blantyre South Center' },
  { id: '3', centerNumber: '003', centerName: 'Mzuzu North Center' },
  { id: '4', centerNumber: '004', centerName: 'Zomba East Center' },
  { id: '5', centerNumber: '005', centerName: 'Kasungu West Center' },
  { id: '6', centerNumber: '006', centerName: 'Mangochi Center' },
  { id: '7', centerNumber: '007', centerName: 'Karonga Center' },
  { id: '8', centerNumber: '008', centerName: 'Salima Center' },
  { id: '9', centerNumber: '009', centerName: 'Dedza Center' },
  { id: '10', centerNumber: '010', centerName: 'Ntcheu Center' }
])

// Mock exam sessions data - These represent exam papers/sessions, not individual student exam numbers
// Individual student exam numbers (like 240001, 240002) are assigned to students for each exam session
const mockExamSessions = [
  { id: '1', name: 'MATH001-2024', subject: 'Mathematics', paper: 'Paper 1 (Pure Mathematics)', session: '2024 Main Session' },
  { id: '2', name: 'MATH002-2024', subject: 'Mathematics', paper: 'Paper 2 (Applied Mathematics)', session: '2024 Main Session' },
  { id: '3', name: 'MATH003-2024', subject: 'Mathematics', paper: 'Paper 1 (Pure Mathematics)', session: '2024 Supplementary Session' },
  { id: '4', name: 'ENG001-2024', subject: 'English Language', paper: 'Paper 1 (Language)', session: '2024 Main Session' },
  { id: '5', name: 'ENG002-2024', subject: 'English Language', paper: 'Paper 2 (Literature)', session: '2024 Main Session' },
  { id: '6', name: 'ENG003-2024', subject: 'English Language', paper: 'Paper 1 (Language)', session: '2024 Supplementary Session' },
  { id: '7', name: 'SCI001-2024', subject: 'Physical Science', paper: 'Paper 1 (Theory)', session: '2024 Main Session' },
  { id: '8', name: 'SCI002-2024', subject: 'Physical Science', paper: 'Paper 2 (Practical)', session: '2024 Main Session' },
  { id: '9', name: 'SCI003-2024', subject: 'Physical Science', paper: 'Paper 3 (Alternative to Practical)', session: '2024 Main Session' },
  { id: '10', name: 'BIO001-2024', subject: 'Biology', paper: 'Paper 1 (Theory)', session: '2024 Main Session' },
  { id: '11', name: 'BIO002-2024', subject: 'Biology', paper: 'Paper 2 (Practical)', session: '2024 Main Session' },
  { id: '12', name: 'CHEM001-2024', subject: 'Chemistry', paper: 'Paper 1 (Theory)', session: '2024 Main Session' },
  { id: '13', name: 'CHEM002-2024', subject: 'Chemistry', paper: 'Paper 2 (Practical)', session: '2024 Main Session' },
  { id: '14', name: 'PHYS001-2024', subject: 'Physics', paper: 'Paper 1 (Theory)', session: '2024 Main Session' },
  { id: '15', name: 'PHYS002-2024', subject: 'Physics', paper: 'Paper 2 (Practical)', session: '2024 Main Session' },
  { id: '16', name: 'GEO001-2024', subject: 'Geography', paper: 'Paper 1 (Physical Geography)', session: '2024 Main Session' },
  { id: '17', name: 'GEO002-2024', subject: 'Geography', paper: 'Paper 2 (Human Geography)', session: '2024 Main Session' },
  { id: '18', name: 'HIST001-2024', subject: 'History', paper: 'Paper 1 (World History)', session: '2024 Main Session' },
  { id: '19', name: 'HIST002-2024', subject: 'History', paper: 'Paper 2 (African History)', session: '2024 Main Session' },
  { id: '20', name: 'ECON001-2024', subject: 'Economics', paper: 'Paper 1 (Microeconomics)', session: '2024 Main Session' }
]

// Methods
const loadAvailableExamSessions = async () => {
  // Set filters for candidates based on selected filters
  if (props.selectedFilters?.centerId) {
    await setCandidateFilters({
      centreId: props.selectedFilters.centerId,
      examTypeId: props.selectedFilters.examTypeId
    })
  }
}

const loadStudentsForExam = async () => {
  if (!selectedExamNumber.value) return

  isLoading.value = true
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    eligibleStudents.value = [...mockStudents]
    studentsLoaded.value = true

    sweetAlert.toast.success(`Students loaded for exam ${getSelectedExamNumberName()}`)
  } catch (error) {
    console.error('Error loading students:', error)
    sweetAlert.error('Error', 'Failed to load students')
  } finally {
    isLoading.value = false
  }
}

const loadAllStudentsForFilters = async () => {
  isLoading.value = true
  try {
    // Mock API call to load all students matching the hierarchical filters
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Filter students based on selected criteria
    const filteredStudents = mockStudents.filter(student => {
      // Filter by subject
      if (props.selectedFilters?.subjectName && student.subjectName !== props.selectedFilters.subjectName) {
        return false
      }

      // Filter by paper
      if (props.selectedFilters?.paperName && student.paperName !== props.selectedFilters.paperName) {
        return false
      }

      // In a real implementation, this would also filter by:
      // props.selectedFilters.division, district, center, school, scoreType, etc.

      return true
    })

    eligibleStudents.value = [...filteredStudents]
    studentsLoaded.value = true

    console.log('Students loaded for filters:', props.selectedFilters)
    console.log('Filtered students count:', filteredStudents.length)
  } catch (error) {
    console.error('Error loading students for filters:', error)
    sweetAlert.error('Error', 'Failed to load students for the selected filters')
  } finally {
    isLoading.value = false
  }
}

// Subject and Paper Selection Methods
const loadSubjects = async () => {
  isLoadingSubjects.value = true
  try {
    // Mock API call - in real implementation, this would fetch from API
    await new Promise(resolve => setTimeout(resolve, 500))
    availableSubjects.value = [
      { id: '1', name: 'Mathematics' },
      { id: '2', name: 'English Language' },
      { id: '3', name: 'Physical Science' },
      { id: '4', name: 'Biology' },
      { id: '5', name: 'Chemistry' },
      { id: '6', name: 'Physics' },
      { id: '7', name: 'Geography' },
      { id: '8', name: 'History' },
      { id: '9', name: 'Economics' }
    ]
  } catch (error) {
    console.error('Error loading subjects:', error)
    sweetAlert.error('Error', 'Failed to load subjects')
  } finally {
    isLoadingSubjects.value = false
  }
}

const loadPapers = async (subjectId: string) => {
  if (!subjectId) {
    availablePapers.value = []
    return
  }

  isLoadingPapers.value = true
  try {
    // Mock API call - in real implementation, this would fetch papers for the selected subject
    await new Promise(resolve => setTimeout(resolve, 300))

    const subjectName = availableSubjects.value.find(s => s.id === subjectId)?.name

    // Mock papers based on subject
    const papersBySubject: Record<string, any[]> = {
      'Mathematics': [
        { id: 'MATH001', name: 'Paper 1 (Pure Mathematics)' },
        { id: 'MATH002', name: 'Paper 2 (Applied Mathematics)' }
      ],
      'English Language': [
        { id: 'ENG001', name: 'Paper 1 (Language)' },
        { id: 'ENG002', name: 'Paper 2 (Literature)' }
      ],
      'Physical Science': [
        { id: 'SCI001', name: 'Paper 1 (Theory)' },
        { id: 'SCI002', name: 'Paper 2 (Practical)' },
        { id: 'SCI003', name: 'Paper 3 (Alternative to Practical)' }
      ],
      'Biology': [
        { id: 'BIO001', name: 'Paper 1 (Theory)' },
        { id: 'BIO002', name: 'Paper 2 (Practical)' }
      ],
      'Chemistry': [
        { id: 'CHEM001', name: 'Paper 1 (Theory)' },
        { id: 'CHEM002', name: 'Paper 2 (Practical)' }
      ],
      'Physics': [
        { id: 'PHYS001', name: 'Paper 1 (Theory)' },
        { id: 'PHYS002', name: 'Paper 2 (Practical)' }
      ],
      'Geography': [
        { id: 'GEO001', name: 'Paper 1 (Physical Geography)' },
        { id: 'GEO002', name: 'Paper 2 (Human Geography)' }
      ],
      'History': [
        { id: 'HIST001', name: 'Paper 1 (World History)' },
        { id: 'HIST002', name: 'Paper 2 (African History)' }
      ],
      'Economics': [
        { id: 'ECON001', name: 'Paper 1 (Microeconomics)' },
        { id: 'ECON002', name: 'Paper 2 (Macroeconomics)' }
      ]
    }

    availablePapers.value = papersBySubject[subjectName || ''] || []
  } catch (error) {
    console.error('Error loading papers:', error)
    sweetAlert.error('Error', 'Failed to load papers')
  } finally {
    isLoadingPapers.value = false
  }
}

const onSubjectChange = () => {
  selectedPaper.value = ''
  loadPapers(selectedSubject.value)

  // Reset students when subject changes
  eligibleStudents.value = []
  studentsLoaded.value = false

  // Reset to first page when subject changes
  currentStudentIndex.value = 0

  console.log('📋 Subject changed - reset to first page')
}

const onPaperChange = () => {
  // Reset to first page when paper changes
  currentStudentIndex.value = 0

  console.log('📄 Paper changed - reset to first page')

  // Load students for the selected subject and paper
  if (selectedSubject.value && selectedPaper.value) {
    loadStudentsForSubjectAndPaper()
  }
}

const loadStudentsForSubjectAndPaper = async () => {
  if (!selectedSubject.value || !selectedPaper.value) return

  isLoading.value = true
  try {
    // Mock API call to load students for selected subject and paper
    await new Promise(resolve => setTimeout(resolve, 1000))

    const subjectName = availableSubjects.value.find(s => s.id === selectedSubject.value)?.name
    const paperName = availablePapers.value.find(p => p.id === selectedPaper.value)?.name

    // Filter mock students by subject and paper
    const filteredMockStudents = mockStudents.filter(student =>
      student.subjectName === subjectName && student.paperName === paperName
    )

    eligibleStudents.value = [...filteredMockStudents]
    studentsLoaded.value = true
    currentStudentIndex.value = 0

    sweetAlert.toast.success(`Students loaded for ${subjectName} - ${paperName}`)
  } catch (error) {
    console.error('Error loading students:', error)
    sweetAlert.error('Error', 'Failed to load students')
  } finally {
    isLoading.value = false
  }
}

// Enhanced Search Methods
const onExamNumberSearchInput = () => {
  showExamNumberDropdown.value = true
  highlightedExamNumberIndex.value = -1

  // Use debounced search for API calls
  if (examNumberSearch.value.length >= 2) {
    debouncedSearch(examNumberSearch.value)
  }

  // Clear selection if search doesn't match current selection
  if (selectedExamNumberData.value && !selectedExamNumberData.value.examNumber.toLowerCase().includes(examNumberSearch.value.toLowerCase())) {
    selectedExamNumber.value = ''
    selectedExamNumberData.value = null
  }
}

const clearExamNumberSearch = () => {
  examNumberSearch.value = ''
  selectedExamNumber.value = ''
  selectedExamNumberData.value = null
  showExamNumberDropdown.value = false
  highlightedExamNumberIndex.value = -1
  onExamNumberChange()
}

const selectExamNumber = (candidate: any) => {
  selectedExamNumber.value = candidate.id
  selectedExamNumberData.value = candidate
  examNumberSearch.value = candidate.examNumber
  showExamNumberDropdown.value = false
  highlightedExamNumberIndex.value = -1
  onExamNumberChange()
}

const onCandidateNameSearchInput = () => {
  showCandidateNameDropdown.value = true
  highlightedCandidateNameIndex.value = -1
}

const clearCandidateNameSearch = () => {
  candidateNameSearch.value = ''
  showCandidateNameDropdown.value = false
  highlightedCandidateNameIndex.value = -1
}

const selectCandidateName = (candidate: any) => {
  candidateNameSearch.value = candidate.fullName
  showCandidateNameDropdown.value = false
  highlightedCandidateNameIndex.value = -1

  // Also update exam number search to match
  examNumberSearch.value = candidate.examNumber
  selectedExamNumber.value = candidate.id
  selectedExamNumberData.value = candidate
}

const onCenterNumberSearchInput = () => {
  showCenterNumberDropdown.value = true
  highlightedCenterNumberIndex.value = -1
}

const clearCenterNumberSearch = () => {
  centerNumberSearch.value = ''
  showCenterNumberDropdown.value = false
  highlightedCenterNumberIndex.value = -1
}

const selectCenterNumber = (center: any) => {
  centerNumberSearch.value = center.centerNumber
  showCenterNumberDropdown.value = false
  highlightedCenterNumberIndex.value = -1
}

const clearAllSearches = () => {
  clearExamNumberSearch()
  clearCandidateNameSearch()
  clearCenterNumberSearch()
}

// Blur handlers for each search field
const handleExamNumberBlur = () => {
  setTimeout(() => {
    showExamNumberDropdown.value = false
    highlightedExamNumberIndex.value = -1
    if (!selectedExamNumber.value) {
      examNumberSearch.value = ''
    }
  }, 150)
}

const handleCandidateNameBlur = () => {
  setTimeout(() => {
    showCandidateNameDropdown.value = false
    highlightedCandidateNameIndex.value = -1
  }, 150)
}

const handleCenterNumberBlur = () => {
  setTimeout(() => {
    showCenterNumberDropdown.value = false
    highlightedCenterNumberIndex.value = -1
  }, 150)
}

// Keydown handlers for each search field
const handleExamNumberKeydown = (event: KeyboardEvent) => {
  if (!showExamNumberDropdown.value) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      highlightedExamNumberIndex.value = Math.min(highlightedExamNumberIndex.value + 1, filteredExamNumbers.value.length - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      highlightedExamNumberIndex.value = Math.max(highlightedExamNumberIndex.value - 1, -1)
      break
    case 'Enter':
      event.preventDefault()
      if (highlightedExamNumberIndex.value >= 0 && filteredExamNumbers.value[highlightedExamNumberIndex.value]) {
        selectExamNumber(filteredExamNumbers.value[highlightedExamNumberIndex.value])
      }
      break
    case 'Escape':
      showExamNumberDropdown.value = false
      highlightedExamNumberIndex.value = -1
      break
  }
}

const handleCandidateNameKeydown = (event: KeyboardEvent) => {
  if (!showCandidateNameDropdown.value) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      highlightedCandidateNameIndex.value = Math.min(highlightedCandidateNameIndex.value + 1, filteredCandidateNames.value.length - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      highlightedCandidateNameIndex.value = Math.max(highlightedCandidateNameIndex.value - 1, -1)
      break
    case 'Enter':
      event.preventDefault()
      if (highlightedCandidateNameIndex.value >= 0 && filteredCandidateNames.value[highlightedCandidateNameIndex.value]) {
        selectCandidateName(filteredCandidateNames.value[highlightedCandidateNameIndex.value])
      }
      break
    case 'Escape':
      showCandidateNameDropdown.value = false
      highlightedCandidateNameIndex.value = -1
      break
  }
}

const handleCenterNumberKeydown = (event: KeyboardEvent) => {
  if (!showCenterNumberDropdown.value) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      highlightedCenterNumberIndex.value = Math.min(highlightedCenterNumberIndex.value + 1, filteredCenterNumbers.value.length - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      highlightedCenterNumberIndex.value = Math.max(highlightedCenterNumberIndex.value - 1, -1)
      break
    case 'Enter':
      event.preventDefault()
      if (highlightedCenterNumberIndex.value >= 0 && filteredCenterNumbers.value[highlightedCenterNumberIndex.value]) {
        selectCenterNumber(filteredCenterNumbers.value[highlightedCenterNumberIndex.value])
      }
      break
    case 'Escape':
      showCenterNumberDropdown.value = false
      highlightedCenterNumberIndex.value = -1
      break
  }
}

const onExamNumberChange = () => {
  studentsLoaded.value = false
  eligibleStudents.value = []
}

// Student search methods
const onStudentSearchInput = () => {
  // Clear any existing timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }

  // Debounce the search to avoid excessive filtering
  searchTimeout.value = setTimeout(() => {
    debouncedSearchQuery.value = studentSearchQuery.value
  }, 300)
}

const clearStudentSearch = () => {
  studentSearchQuery.value = ''
  debouncedSearchQuery.value = ''
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
    searchTimeout.value = null
  }
}

// Completed scores search methods
const clearCompletedScoresSearch = () => {
  completedScoresSearchQuery.value = ''
}

// Search debounce for completed scores
let completedScoresSearchTimeout: NodeJS.Timeout | null = null
const debouncedCompletedScoresSearch = (query: string) => {
  if (completedScoresSearchTimeout) {
    clearTimeout(completedScoresSearchTimeout)
  }

  completedScoresSearchTimeout = setTimeout(async () => {
    // If search query is provided, we could implement server-side search here
    // For now, we use client-side filtering within current page
    console.log('🔍 Searching completed scores:', query)
  }, 300)
}

// Watch for search query changes
watch(completedScoresSearchQuery, (newQuery) => {
  debouncedCompletedScoresSearch(newQuery)
})

// Pagination helper methods
const getVisiblePages = () => {
  const current = completedScoresPagination.value.pageNumber
  const total = completedScoresPagination.value.totalPages
  const pages: (number | string)[] = []

  if (total <= 7) {
    // Show all pages if total is 7 or less
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // Always show first page
    pages.push(1)

    if (current <= 4) {
      // Show pages 2-5 and ellipsis
      for (let i = 2; i <= 5; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    } else if (current >= total - 3) {
      // Show ellipsis and last 5 pages
      pages.push('...')
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      // Show ellipsis, current page with neighbors, ellipsis, last page
      pages.push('...')
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    }
  }

  return pages
}

const onPageSizeChange = async () => {
  // Persist page size selection
  localStorage.setItem('maneb-completed-scores-page-size', completedScoresPagination.value.pageSize.toString())

  // Reset to first page when page size changes
  completedScoresPagination.value.pageNumber = 1
  await loadCompletedScores(1)
}

const jumpToPage = async () => {
  if (jumpToPageNumber.value &&
      jumpToPageNumber.value >= 1 &&
      jumpToPageNumber.value <= completedScoresPagination.value.totalPages) {
    await loadCompletedScores(jumpToPageNumber.value)
    jumpToPageNumber.value = null // Clear the input after successful jump
  }
}

// Load completed scores from API
const loadCompletedScores = async (pageNumber: number = 1) => {
  if (!props.selectedFilters) {
    console.log('No filters selected, skipping completed scores load')
    return
  }

  completedScoresLoading.value = true

  try {
    console.log('🔄 Loading completed scores from API...')

    // Get the center number using the same logic as loadCandidatesFromSummary
    let centerNumber = props.selectedFilters.centerNumber ||
                      props.selectedFilters.manebcentreNo ||
                      props.selectedFilters.centerNo

    // If we don't have a center number but we have a center ID, try to extract it
    if (!centerNumber && props.selectedFilters.center) {
      console.log('🔍 Attempting to extract center number from center ID for completed scores:', props.selectedFilters.center)
      centerNumber = props.selectedFilters.center
    }

    console.log('🔍 Center detection for completed scores:', {
      centerNumber,
      centerNumberProp: props.selectedFilters.centerNumber,
      manebcentreNoProp: props.selectedFilters.manebcentreNo,
      centerNoProp: props.selectedFilters.centerNo,
      centerId: props.selectedFilters.center,
      allFilterKeys: Object.keys(props.selectedFilters)
    })

    if (!centerNumber) {
      console.warn('⚠️ Center number not found for completed scores. Skipping load.')
      completedScores.value = []
      completedScoresPagination.value = {
        pageNumber: 1,
        pageSize: 10,
        totalCount: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPreviousPage: false
      }
      return
    }

    // Prepare API parameters using the same filters as student loading
    const apiParams = {
      creator: authStore.user?.id, // Only show scores created by current user
      centerNo: centerNumber, // Use the detected center number
      schoolId: props.selectedFilters.schoolId,
      examTypeId: props.selectedFilters.examTypeId,
      examYear: props.selectedFilters.examYear,
      subjectId: props.selectedFilters.subjectId || props.selectedFilters.subject,
      pageNumber: pageNumber,
      pageSize: completedScoresPagination.value.pageSize
    }

    console.log('📊 API parameters for completed scores:', apiParams)

    const response = await gradingApiStore.getPaginatedScoreEntries(apiParams)

    console.log('✅ Completed scores API response:', response)

    // Map API response to CompletedScore interface
    const mappedScores: CompletedScore[] = response.items.map(item => ({
      id: item.id,
      examinationNumber: item.examinationNumber,
      paperId: item.paperId,
      score: item.score,
      scoreType: item.scoreType,
      studentStatus: item.studentStatus === 'Present' ? 'Present' :
                   item.studentStatus === 'Absent' ? 'Absent' : 'Missing',
      status: item.status,
      dateCreated: item.dateCreated,
      createdBy: item.createdBy,
      correctionNote: item.correctionNote || ''
    }))

    // Update state
    if (pageNumber === 1) {
      completedScores.value = mappedScores
    } else {
      completedScores.value.push(...mappedScores)
    }

    // Update pagination info
    completedScoresPagination.value = {
      pageNumber: response.pageNumber,
      pageSize: response.pageSize,
      totalCount: response.totalCount,
      totalPages: response.totalPages,
      hasNextPage: response.hasNextPage,
      hasPreviousPage: response.hasPreviousPage
    }

    showCompletedScores.value = completedScores.value.length > 0

    console.log(`✅ Loaded ${mappedScores.length} completed scores (page ${pageNumber}/${response.totalPages})`)

  } catch (error) {
    console.error('❌ Error loading completed scores:', error)
    // Don't show error to user, just log it
  } finally {
    completedScoresLoading.value = false
  }
}

// Score input methods
const onScoreInput = (student: Student) => {
  // Handle dot notation for attendance status
  const input = student.tempScoreInput?.trim() || ''

  if (input === '.') {
    // Single dot = Absent
    student.tempScore = null
    student.studentStatus = 'Absent'
    student.hasUnsavedChanges = true
    console.log('🔴 Dot notation detected: Absent status for', student.examNumber)
  } else if (input === '..') {
    // Double dots = Missing
    student.tempScore = null
    student.studentStatus = 'Missing'
    student.hasUnsavedChanges = true
    console.log('🔴 Double dot notation detected: Missing status for', student.examNumber)
  } else if (input === '') {
    // Empty input = reset to Present
    student.tempScore = null
    student.studentStatus = 'Present'
    student.hasUnsavedChanges = true
  } else {
    // Try to parse as number
    const numericValue = parseFloat(input)
    if (!isNaN(numericValue) && numericValue >= 0 && numericValue <= 100) {
      student.tempScore = numericValue
      student.studentStatus = 'Present'
      student.hasUnsavedChanges = true
    } else if (input !== '') {
      // Invalid input - keep the input but don't set tempScore
      student.tempScore = null
      student.studentStatus = 'Present'
      student.hasUnsavedChanges = true
    }
  }
}

const resetScore = (student: Student) => {
  // Reset to original score on Escape
  student.tempScore = student.score
  student.tempScoreInput = student.score?.toString() || ''
  student.studentStatus = 'Present'
  student.hasUnsavedChanges = false
}

const getSelectedExamNumberName = () => {
  return selectedExamNumberData.value?.name || ''
}



const getScoreColor = (score: number | null): string => {
  if (score === null) return 'text-gray-400'
  if (score >= 80) return 'text-green-600'
  if (score >= 70) return 'text-blue-600'
  if (score >= 60) return 'text-yellow-600'
  if (score >= 50) return 'text-orange-600'
  return 'text-red-600'
}

const saveScore = async (student: Student) => {
  // Allow submission for valid scores OR attendance status (absent/missing)
  const isValidScore = student.tempScore !== null && student.tempScore >= 0 && student.tempScore <= 100
  const isAttendanceStatus = student.studentStatus === 'Absent' || student.studentStatus === 'Missing'

  if (isValidScore || isAttendanceStatus) {
    try {
      // Initialize grading API service
      gradingApiStore.initializeApiService()

      // Set submitting state
      isSubmittingToApi.value = true

      // Get the center number using comprehensive detection logic
      let centerNumber = props.selectedFilters?.centerNumber ||
                        props.selectedFilters?.manebcentreNo ||
                        props.selectedFilters?.centerNo ||
                        student.centreNo // Try to get from student data

      // If we don't have a center number but we have a center ID, try to extract it
      if (!centerNumber && props.selectedFilters?.center) {
        centerNumber = props.selectedFilters.center
      }

      // Debug logging to see what's available
      console.log('🔍 Center number detection for score submission:', {
        finalCenterNumber: centerNumber,
        centerNumberProp: props.selectedFilters?.centerNumber,
        manebcentreNoProp: props.selectedFilters?.manebcentreNo,
        centerNoProp: props.selectedFilters?.centerNo,
        centerId: props.selectedFilters?.center,
        studentCentreNo: student.centreNo,
        allFilterKeys: Object.keys(props.selectedFilters || {}),
        studentKeys: Object.keys(student || {})
      })

      if (!centerNumber) {
        console.error('❌ Center number is null/undefined for score submission. Available data:', {
          selectedFilters: props.selectedFilters,
          student: student
        })
        // Don't throw error, but use empty string as fallback
        centerNumber = ''
      }

      // Prepare score entry data for grading API with all required fields
      const scoreEntryData: CreateScoreEntryRequest = {
        paperId: getScriptId(), // Get paper ID from selected filters
        examinationNumber: student.examNumber, // Required - Student examination number
        userId: authStore.user?.id || '', // Current authenticated user
        score: student.tempScore, // Will be null for absent/missing students
        scoreType: selectedScoreType.value, // Use selected score type (required)
        correctionNote: student.notes || '', // Optional correction note (empty string if not provided)
        studentStatus: student.studentStatus || 'Present', // Include attendance status
        // Additional required parameters for querying back
        centerNo: centerNumber || '', // Required - Center number
        candidateId: student.id, // Required - Candidate ID
        schoolId: student.schoolId || props.selectedFilters.schoolId || '', // Required - School ID
        examYear: student.examYear || props.selectedFilters.examYear || new Date().getFullYear() // Required - Examination year
      }

      console.log('📤 Submitting score entry with all required parameters:', scoreEntryData)
      console.log('🔍 Score entry parameters breakdown:', {
        centerNo: scoreEntryData.centerNo,
        candidateId: scoreEntryData.candidateId,
        schoolId: scoreEntryData.schoolId,
        examYear: scoreEntryData.examYear,
        examinationNumber: scoreEntryData.examinationNumber,
        score: scoreEntryData.score,
        studentStatus: scoreEntryData.studentStatus,
        scoreType: scoreEntryData.scoreType,
        paperId: scoreEntryData.paperId,
        userId: scoreEntryData.userId
      })

      // Submit to grading API and get the response
      const apiResponse = await gradingApiStore.submitScore(scoreEntryData)

      // Update local state after successful API submission
      student.score = student.tempScore
      student.hasUnsavedChanges = false

      // Refresh completed scores from API to show the latest data
      await loadCompletedScores(1)

      // Clear the score input
      student.tempScore = null
      student.tempScoreInput = ''

      // Emit score update for local system integration
      emit('score-updated', {
        studentId: student.id,
        score: student.score,
        examNumber: student.examNumber,
        auditData: {
          action: 'SCORE_ENTRY',
          studentId: student.studentId,
          examNumber: student.examNumber,
          subject: student.subjectName,
          score: student.score,
          timestamp: new Date().toISOString(),
          apiSubmitted: true // Flag to indicate API submission
        }
      })

      // Advance to next student in sequential entry
      advanceToNextStudent()

      // Use toast notification for score entry operations
      const message = student.studentStatus === 'Absent'
        ? `Student marked as Absent for exam ${student.examNumber}`
        : student.studentStatus === 'Missing'
        ? `Student marked as Missing for exam ${student.examNumber}`
        : `Score ${student.score}% saved for exam ${student.examNumber}`
      showToast(message, 'success')

    } catch (error: any) {
      console.error('Error submitting score to grading API:', error)

      // Handle API submission error gracefully - still save locally
      student.score = student.tempScore
      student.hasUnsavedChanges = false

      // Note: Not adding to completed scores since API submission failed
      // Only scores successfully submitted to API will appear in completed scores table

      // Clear the score input
      student.tempScore = null
      student.tempScoreInput = ''

      // Emit score update for local system (fallback)
      emit('score-updated', {
        studentId: student.id,
        score: student.score,
        examNumber: student.examNumber,
        auditData: {
          action: 'SCORE_ENTRY',
          studentId: student.studentId,
          examNumber: student.examNumber,
          subject: student.subjectName,
          score: student.score,
          timestamp: new Date().toISOString(),
          apiSubmitted: false, // Flag to indicate API submission failed
          apiError: error.message || 'API submission failed'
        }
      })

      // Advance to next student in sequential entry
      advanceToNextStudent()

      // Show warning but still indicate local save success
      showToast(`Score ${student.score}% saved locally for exam ${student.examNumber}. API sync failed - will retry later.`, 'warning')

    } finally {
      // Reset submitting state
      isSubmittingToApi.value = false
    }
  } else {
    sweetAlert.error('Invalid Input', 'Please enter:\n• A score between 0 and 100\n• Single dot (.) for Absent\n• Double dots (..) for Missing')
  }
}

// Sequential entry navigation
const advanceToNextStudent = () => {
  // Remove the current student from the eligible students array
  const currentStudent = filteredStudents.value[0]
  if (currentStudent) {
    const studentIndex = eligibleStudents.value.findIndex(s => s.id === currentStudent.id)
    if (studentIndex !== -1) {
      eligibleStudents.value.splice(studentIndex, 1)
    }
  }

  // Reset current index to 0 to show the next student
  currentStudentIndex.value = 0

  // Focus on the next student's score input if available
  setTimeout(() => {
    const nextInput = document.querySelector(`input[data-student-id]`) as HTMLInputElement
    if (nextInput) {
      nextInput.focus()
    }
  }, 100)
}

const saveAllScores = async () => {
  isSaving.value = true
  try {
    // Mock API call to save all changes
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Mark all changes as saved
    eligibleStudents.value.forEach(student => {
      student.hasUnsavedChanges = false
    })
    
    sweetAlert.toast.success('All scores have been saved successfully')
  } catch (error) {
    console.error('Error saving scores:', error)
    sweetAlert.error('Error', 'Failed to save scores')
  } finally {
    isSaving.value = false
  }
}

const viewStudent = (student: Student) => {
  sweetAlert.info('Student Details', `Viewing details for ${student.fullName}`)
}

const editFilters = () => {
  emit('edit-filters')
}



const openNotesModal = (student: Student) => {
  selectedStudentForNotes.value = student
  currentNotes.value = student.notes
  isNotesModalOpen.value = true
}

const closeNotesModal = () => {
  isNotesModalOpen.value = false
  selectedStudentForNotes.value = null
  currentNotes.value = ''
}

const saveNotes = async () => {
  if (!selectedStudentForNotes.value) return

  isSavingNotes.value = true

  try {
    // Mock API call to save notes
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Update the student's notes
    selectedStudentForNotes.value.notes = currentNotes.value
    selectedStudentForNotes.value.hasNotes = currentNotes.value.trim() !== ''

    sweetAlert.toast.success('Student notes have been saved successfully.')
    closeNotesModal()

  } catch (error) {
    console.error('Error saving notes:', error)
    sweetAlert.error('Error', 'Failed to save notes. Please try again.')
  } finally {
    isSavingNotes.value = false
  }
}

// Helper methods for filter names
const getDivisionName = () => props.selectedFilters?.divisionName || 'Unknown Division'
const getDistrictName = () => props.selectedFilters?.districtName || 'Unknown District'
const getCenterName = () => props.selectedFilters?.centerName || 'Unknown Center'
const getExamTypeName = () => props.selectedFilters?.examTypeName || 'Unknown Exam Type'
const getSchoolName = () => props.selectedFilters?.schoolName || 'Unknown School'
const getSubjectName = () => props.selectedFilters?.subjectName || 'Unknown Subject'
const getPaperName = () => props.selectedFilters?.paperName  || 'Unknown Paper'
const getScoreTypeName = () => props.selectedFilters?.scoreTypeName || 'Unknown Score Type'

// Permission checks
const {
  canViewScores,
  canCreateScores,
  canEditScores,
  canDeleteScores,
  canApproveScores
} = useScoreEntryPermissions()

// Delete score function
const deleteScore = async (student: Student) => {
  if (!canDeleteScores.value) {
    sweetAlert.error('Access Denied', 'You do not have permission to delete scores.')
    return
  }

  const result = await sweetAlert.confirm(
    'Delete Score',
    `Are you sure you want to delete the score for ${student.fullName}?`,
    'Delete',
    'Cancel'
  )

  if (result.isConfirmed) {
    try {
      // Reset the student's score
      student.score = null
      student.tempScore = null
      student.tempScoreInput = ''
      student.hasUnsavedChanges = false

      // Refresh completed scores from API to reflect the deletion
      await loadCompletedScores(1)

      sweetAlert.success('Score Deleted', 'The score has been successfully deleted.')
    } catch (error) {
      console.error('Error deleting score:', error)
      sweetAlert.error('Error', 'Failed to delete score. Please try again.')
    }
  }
}

// Watch for filter changes and reload students
watch(() => props.selectedFilters, async (newFilters, oldFilters) => {
  if (newFilters && (
    newFilters.center !== oldFilters?.center ||
    newFilters.subject !== oldFilters?.subject ||
    newFilters.examYear !== oldFilters?.examYear ||
    newFilters.subjectName !== oldFilters?.subjectName ||
    newFilters.paperName !== oldFilters?.paperName ||
    newFilters.scoreTypeName !== oldFilters?.scoreTypeName
  )) {
    console.log('Filters changed, reloading candidates and students:', newFilters)
    // Clear completed scores when filters change (new session)
    completedScores.value = []
    showCompletedScores.value = false
    completedScoresPagination.value.pageNumber = 1

    // Clear school information when filters change
    loadedSchoolName.value = ''
    loadedCenterNumber.value = ''
    loadedDistrictName.value = ''

    await loadCandidatesFromSummary()
    loadAllStudentsForFilters()
    // Load completed scores for the new filters
    await loadCompletedScores(1)
  }
}, { deep: true })

// Function to get current sequence number for display
const getCurrentSequenceNumber = () => {
  const currentStudent = filteredStudents.value[0]
  return currentStudent?.sequenceNo || 1
}

// Function to jump to a specific sequence number
const jumpToSequenceNumber = () => {
  if (!startFromSequenceNo.value || startFromSequenceNo.value < 1) {
    return
  }

  const targetSequence = startFromSequenceNo.value
  const sortedList = sortedStudents.value

  // Find the index of the student with the target sequence number
  const targetIndex = sortedList.findIndex(student => student.sequenceNo === targetSequence)

  if (targetIndex !== -1) {
    // Remove all students before the target sequence from eligibleStudents
    const studentsToRemove = sortedList.slice(0, targetIndex)
    studentsToRemove.forEach(studentToRemove => {
      const indexInEligible = eligibleStudents.value.findIndex(s => s.id === studentToRemove.id)
      if (indexInEligible !== -1) {
        eligibleStudents.value.splice(indexInEligible, 1)
      }
    })

    // Reset current index to 0 to show the target student
    currentStudentIndex.value = 0

    // Clear the input
    startFromSequenceNo.value = null

    // Focus on the score input for the new current student
    setTimeout(() => {
      const scoreInput = document.querySelector(`input[data-student-id]`) as HTMLInputElement
      if (scoreInput) {
        scoreInput.focus()
      }
    }, 100)
  } else {
    // Show error if sequence number not found
    sweetAlert.error('Sequence Not Found', `No candidate found with sequence number ${targetSequence}`)
  }
}

onMounted(async () => {
  // Load candidates from Candidate endpoint based on selected filters
  await loadCandidatesFromSummary()

  // Load completed scores for the initial filters
  await loadCompletedScores(1)

  // Load subjects for the new workflow (if needed for legacy compatibility)
  // await loadSubjects()

  // Generate students with real paper codes from API (if needed for legacy compatibility)
  // await generateStudentsWithPaperCodes()

  // await loadAvailableExamSessions()
})

onUnmounted(() => {
  // Cleanup search timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
})
</script>

<style scoped>
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary\/50:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

/* Searchable dropdown styles */
.searchable-dropdown {
  position: relative;
}

.dropdown-list {
  max-height: 240px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #a12c2c #f1f1f1;
}

.dropdown-list::-webkit-scrollbar {
  width: 6px;
}

.dropdown-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dropdown-list::-webkit-scrollbar-thumb {
  background: #a12c2c;
  border-radius: 3px;
}

.dropdown-list::-webkit-scrollbar-thumb:hover {
  background: #8b2424;
}

.dropdown-item-highlighted {
  background-color: #a12c2c !important;
  color: white !important;
}

.dropdown-item-highlighted .text-gray-500 {
  color: rgba(255, 255, 255, 0.8) !important;
}
</style>
