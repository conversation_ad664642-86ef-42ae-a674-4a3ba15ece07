<template>
  <!-- MANEB Education Management System Login with Background -->
  <div class="min-h-screen bg-cover bg-center bg-no-repeat relative"
       style="background-image: url('/images/hotel-bell.jpg');">

    <!-- Background Overlay -->
    <div class="absolute inset-0" style="background-color: #a12c2c; opacity: 0.6;"></div>

    <!-- Login Section -->
    <section class="relative z-10 min-h-screen flex items-center justify-center">
      <div class="flex flex-col items-center justify-center px-6 py-8 mx-auto w-full max-w-md">

        <!-- MANEB Logo and Title -->
        <div class="flex flex-col items-center mb-8">
          <img class="w-28 h-32 mb-2 drop-shadow-lg logo-float" src="@/assets/logo.png" alt="MANEB Logo">
          <div class="text-center">
            <h1 class="text-3xl font-bold text-white mb-2 drop-shadow-md">MANEB</h1>
            <p class="text-lg text-white opacity-90 drop-shadow-sm">Malawi National Examinations Board</p>
            <p class="text-sm text-white opacity-80 mt-1">Results Management System</p>
          </div>
        </div>

        <!-- Login Card -->
        <div class="w-full bg-white/95 backdrop-blur-sm rounded-lg shadow-2xl border border-white/20 md:mt-0 sm:max-w-md xl:p-0">
          <div class="p-6 space-y-4 md:space-y-6 sm:p-8">
            <div class="text-center">
              <h2 class="text-xl font-bold leading-tight tracking-tight text-maneb-primary md:text-2xl">
                Sign in to MANEB Portal
              </h2>
              <p class="text-sm text-gray-600 mt-2">Access the Results Management System</p>
            </div>



            <!-- Login Form -->
            <form @submit.prevent="handleLogin" class="space-y-4 md:space-y-6">
              <!-- Email Field -->
              <div>
                <label for="email" class="block mb-2 text-sm font-medium text-gray-900">Email Address</label>
                <input
                  type="email"
                  name="email"
                  id="email"
                  v-model="loginForm.email"
                  class="bg-gray-50 border border-gray-300 text-gray-900 rounded-lg focus:ring-maneb-secondary focus:border-maneb-secondary block w-full p-2.5 transition-colors"
                  placeholder="<EMAIL>"
                  required
                  :disabled="isLoading"
                >
              </div>

              <!-- Password Field -->
              <div>
                <label for="password" class="block mb-2 text-sm font-medium text-gray-900">Password</label>
                <input
                  type="password"
                  name="password"
                  id="password"
                  v-model="loginForm.password"
                  placeholder="••••••••"
                  class="bg-gray-50 border border-gray-300 text-gray-900 rounded-lg focus:ring-maneb-secondary focus:border-maneb-secondary block w-full p-2.5 transition-colors"
                  required
                  :disabled="isLoading"
                >
              </div>

              <!-- Remember Me & Forgot Password -->
              <div class="flex items-center justify-between">
                <div class="flex items-start">
                  <div class="flex items-center h-5">
                    <input
                      id="remember"
                      aria-describedby="remember"
                      type="checkbox"
                      v-model="loginForm.rememberMe"
                      class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-maneb-secondary text-maneb-primary"
                      :disabled="isLoading"
                    >
                  </div>
                  <div class="ml-3 text-sm">
                    <label for="remember" class="text-gray-600">Remember me</label>
                  </div>
                </div>
                <router-link to="/auth/forgot-password" class="text-sm font-medium text-maneb-secondary hover:text-maneb-primary hover:underline transition-colors">
                  Forgot password?
                </router-link>
              </div>

              <!-- Submit Button -->
              <button
                type="submit"
                :disabled="isLoading"
                class="w-full text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:ring-4 focus:outline-none focus:ring-maneb-secondary font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
              >
                <span v-if="isLoading" class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Signing in to MANEB...
                </span>
                <span v-else>Sign in to MANEB Portal</span>
              </button>

              <!-- Register Link -->
              <p class="text-sm font-light text-gray-600 text-center">
                Need access to the system?
                <router-link to="/auth/register" class="font-medium text-maneb-secondary hover:text-maneb-primary hover:underline transition-colors">
                  Contact Administrator
                </router-link>
              </p>
            </form>
          </div>
        </div>



        <!-- Footer -->
        <div class="mt-8 text-center">
          <p class="text-white text-sm opacity-80">
            © 2024 Malawi National Examinations Board. All rights reserved.
          </p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/store'
import type { LoginModel } from '@/interfaces'
import sweetAlert from '@/utils/sweetAlert'

// Router and store
const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// Reactive state
const loginForm = ref<LoginModel>({
  email: '',
  password: '',
  rememberMe: false
})

// Computed properties
const isLoading = computed(() => authStore.isLoading)
const error = computed(() => authStore.error)

// Methods
const handleLogin = async () => {
  try {
    authStore.clearError()

    // Show loading alert
    sweetAlert.loading('Signing in...', 'Please wait while we authenticate your credentials')

    await authStore.login(loginForm.value)

    // Close loading
    sweetAlert.close()

    // User info logging removed

    // Show success message first
    const userRole = authStore.userRole
    const welcomeMessage = userRole === 'admin'
      ? `Welcome back, Administrator ${authStore.user?.firstName}!`
      : `Welcome back, ${authStore.userFullName}!`

    await sweetAlert.toast.success(welcomeMessage)

    // Small delay to ensure auth state is fully updated
    setTimeout(async () => {
      // Determine redirect path based on user role
      let redirectPath: string

      if (route.query.redirect) {
        // Use the intended redirect path if provided
        redirectPath = route.query.redirect as string
      } else {
        // Role-based default routing
        redirectPath = authStore.getDashboardRoute
      }

      console.log('Redirecting to:', redirectPath)

      // Force navigation using replace to avoid history issues
      await router.replace(redirectPath)
    }, 500)

  } catch (err: any) {
    // Close loading alert
    sweetAlert.close()

    // Clear any stored authentication data on login failure
    authStore.clearError()
    authStore.logout() // This will clear tokens and user data

    // Show error with SweetAlert
    const errorMessage = err?.response?.data?.message || err?.message || 'Login failed. Please check your credentials.'
    await sweetAlert.error('Login Failed', errorMessage)

    // Clear the form password for security
    loginForm.value.password = ''

    // Ensure we stay on login page and clear any redirect params
    await router.replace({ name: 'auth.login' })

    console.error('Login failed:', err)
  }
}

// Clear any existing errors and authentication state when component mounts
authStore.clearError()

// If user was redirected here due to auth failure, show a message
if (route.query.authFailure) {
  setTimeout(() => {
    sweetAlert.warning('Session Expired', 'Your session has expired. Please log in again.')
  }, 500)
}
</script>

<style scoped>
/* MANEB Official Color Scheme */
.bg-maneb-primary {
  background-color: #a12c2c; /* MANEB Custom Red */
}

.bg-maneb-primary-dark {
  background-color: #8b2424; /* Darker MANEB Red */
}

.text-maneb-primary {
  color: #a12c2c; /* MANEB Custom Red */
}

.text-maneb-secondary {
  color: #D97706; /* MANEB Gold/Yellow */
}

.border-maneb-primary {
  border-color: #a12c2c; /* MANEB Custom Red */
}

.border-maneb-secondary {
  border-color: #D97706; /* MANEB Gold/Yellow */
}

/* Focus states with MANEB colors */
.focus\:ring-maneb-secondary:focus {
  --tw-ring-color: rgba(217, 119, 6, 0.3); /* MANEB Gold with opacity */
}

.focus\:border-maneb-secondary:focus {
  border-color: #D97706; /* MANEB Gold/Yellow */
}

.hover\:text-maneb-primary:hover {
  color: #DC2626; /* MANEB Red */
}

/* Background overlay with MANEB red */
.bg-maneb-primary {
  background-color: #DC2626;
}

/* Custom checkbox styling for MANEB colors */
input[type="checkbox"]:checked {
  background-color: #DC2626;
  border-color: #DC2626;
}

/* Enhanced shadow and transition effects */
.shadow-maneb {
  box-shadow: 0 10px 25px -5px rgba(220, 38, 38, 0.1), 0 10px 10px -5px rgba(220, 38, 38, 0.04);
}

/* Responsive background image */
@media (max-width: 768px) {
  .bg-cover {
    background-size: cover;
    background-position: center;
  }
}

/* Logo animation */
@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

.logo-float {
  animation: logoFloat 3s ease-in-out infinite;
}

/* Backdrop blur support */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}
</style>
