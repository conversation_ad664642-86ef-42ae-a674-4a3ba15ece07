<template>
  <div class="bg-white shadow rounded-lg p-6 m-4">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">Navigation Permissions Debug</h2>
    
    <!-- User Info -->
    <div class="mb-6">
      <h3 class="text-md font-medium text-gray-700 mb-2">User Information</h3>
      <div class="bg-gray-50 p-3 rounded">
        <p><strong>Name:</strong> {{ authStore.userFullName }}</p>
        <p><strong>Role:</strong> {{ authStore.userRole }}</p>
        <p><strong>Is System Admin:</strong> {{ authStore.isSystemAdministrator }}</p>
        <p><strong>Has Admin Privileges:</strong> {{ authStore.hasAdminPrivileges }}</p>
        <p><strong>Total Permissions:</strong> {{ authStore.userPermissions.length }}</p>
      </div>
    </div>

    <!-- Permission Summary -->
    <div class="mb-6">
      <h3 class="text-md font-medium text-gray-700 mb-2">Permission Summary</h3>
      <div class="bg-gray-50 p-3 rounded">
        <pre class="text-sm">{{ JSON.stringify(permissionSummary, null, 2) }}</pre>
      </div>
    </div>

    <!-- Accessible Modules -->
    <div class="mb-6">
      <h3 class="text-md font-medium text-gray-700 mb-2">Accessible Modules</h3>
      <div class="grid grid-cols-2 gap-2">
        <div v-for="module in Object.values(PermissionModule)" :key="module" 
             :class="[
               'p-2 rounded text-sm',
               hasModulePermission(module) 
                 ? 'bg-green-100 text-green-800' 
                 : 'bg-red-100 text-red-800'
             ]">
          <div class="font-medium">{{ module }}</div>
          <div class="text-xs">
            {{ hasModulePermission(module) ? 'Accessible' : 'No Access' }}
          </div>
          <div v-if="hasModulePermission(module)" class="text-xs mt-1">
            Actions: {{ getModuleActions(module).join(', ') }}
          </div>
        </div>
      </div>
    </div>

    <!-- Raw User Permissions -->
    <div class="mb-6">
      <h3 class="text-md font-medium text-gray-700 mb-2">Raw User Permissions</h3>
      <div class="bg-gray-50 p-3 rounded max-h-64 overflow-y-auto">
        <div v-if="authStore.userPermissions.length === 0" class="text-gray-500 italic">
          No permissions loaded
        </div>
        <div v-else>
          <div v-for="(permission, index) in authStore.userPermissions" :key="index" 
               class="mb-2 p-2 bg-white rounded border">
            <div class="text-sm">
              <strong>Section:</strong> {{ permission.sectionID }}<br>
              <strong>Action:</strong> {{ permission.action }}<br>
              <strong>Can Access:</strong> {{ permission.canAccess }}<br>
              <strong>Status:</strong> {{ permission.status }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Test Actions -->
    <div class="mb-6">
      <h3 class="text-md font-medium text-gray-700 mb-2">Test Actions</h3>
      <div class="space-x-2">
        <button @click="refreshPermissions" 
                class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
          Refresh Permissions
        </button>
        <button @click="logNavigationState" 
                class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
          Log Navigation State
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/store/auth.store'
import { useNavigationPermissions } from '@/composables/useNavigationPermissions'
import { PermissionModule } from '@/utils/permissions'

const authStore = useAuthStore()
const { 
  hasModulePermission, 
  getModuleActions, 
  permissionSummary 
} = useNavigationPermissions()

const refreshPermissions = async () => {
  try {
    await authStore.loadUserPermissions()
  } catch (error) {
  }
}

const logNavigationState = () => {
}
</script>

<style scoped>
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
