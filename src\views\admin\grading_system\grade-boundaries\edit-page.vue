<template>
  <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-6">
    <!-- Enhanced <PERSON>er with MANEB Branding -->
    <div class="mb-8">
      <nav class="flex mb-5" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 text-sm font-medium md:space-x-2">
          <li class="inline-flex items-center">
            <router-link
              to="/admin/dashboard"
              class="inline-flex items-center text-gray-700 hover:text-maneb-primary dark:text-gray-300 dark:hover:text-white transition-colors duration-200"
            >
              <svg class="w-5 h-5 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
              </svg>
              Admin Dashboard
            </router-link>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <router-link
                to="/admin/grading-system"
                class="ml-1 text-gray-700 hover:text-maneb-primary md:ml-2 dark:text-gray-300 dark:hover:text-white transition-colors duration-200"
              >
                Grading System
              </router-link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="ml-1 text-gray-400 md:ml-2 dark:text-gray-500" aria-current="page">Edit Grade Boundary</span>
            </div>
          </li>
        </ol>
      </nav>
    </div>

    <!-- Enhanced Loading State -->
    <div v-if="isLoading" class="flex flex-col justify-center items-center py-16">
      <div class="animate-spin rounded-full h-16 w-16 border-4 border-gray-200 border-t-maneb-primary"></div>
      <p class="mt-4 text-sm text-gray-600 dark:text-gray-400">Loading grade boundary information...</p>
    </div>

    <!-- Enhanced Error State -->
    <div v-else-if="loadError" class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6 dark:bg-red-900/20 dark:border-red-800">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-6 w-6 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3 flex-1">
          <h3 class="text-sm font-medium text-red-800 dark:text-red-400">Error loading grade boundary</h3>
          <div class="mt-2 text-sm text-red-700 dark:text-red-300">{{ loadError }}</div>
          <div class="mt-4">
            <button
              @click="fetchGradeBoundaryData"
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Edit Grade Boundary Form -->
    <div v-else-if="gradeBoundary" class="space-y-8">
      <!-- Enhanced Header -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-6">
          <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <!-- Grade Boundary Info -->
            <div class="flex items-center space-x-4">
              <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <span class="text-xl font-bold text-maneb-primary">
                  {{ gradeBoundary.gradeLevel }}
                </span>
              </div>
              <div>
                <h1 class="text-3xl font-bold text-gray-900">Edit Grade Boundary</h1>
                <p class="mt-1 text-sm text-gray-600">
                  Update grade boundary for {{ gradeBoundary.examType }} - {{ getSubjectName(gradeBoundary.subjectId) }}
                </p>
                <div class="mt-2 flex items-center space-x-4">
                  <span :class="gradeBoundary.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                    {{ gradeBoundary.isActive ? 'Active' : 'Inactive' }}
                  </span>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Year {{ gradeBoundary.year }}
                  </span>
                </div>
              </div>
            </div>

            <!-- MANEB Logo/Branding -->
            <div class="hidden md:flex items-center space-x-3">
              <div class="flex items-center space-x-2 px-3 py-2 bg-red-50 rounded-lg border border-red-100">
                <div class="w-8 h-8 bg-maneb-primary rounded-full flex items-center justify-center">
                  <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <span class="text-sm font-medium text-maneb-primary">MANEB EMS</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Edit Form -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
        <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-maneb-primary" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Update Grade Boundary Information</h3>
              <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Modify the grade boundary details below and save changes.</p>
            </div>
          </div>
        </div>

        <form @submit.prevent="handleSubmit" class="px-6 py-6 space-y-8">
          <!-- Basic Information Section -->
          <div class="space-y-6">
            <div class="border-l-4 border-maneb-primary pl-4">
              <h4 class="text-lg font-semibold text-gray-900 dark:text-white">Basic Information</h4>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Configure the exam type, subject, and year</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Exam Type -->
              <div class="space-y-2">
                <label for="examType" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Exam Type <span class="text-red-500">*</span>
                </label>
                <select
                  id="examType"
                  v-model="formData.examType"
                  required
                  :class="[
                    'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                    'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                    'dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400',
                    errors.examType
                      ? 'border-red-300 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 bg-white text-gray-900'
                  ]"
                >
                  <option value="">Select exam type</option>
                  <option v-for="examType in examTypes" :key="examType.id" :value="examType.name">
                    {{ examType.name }}
                  </option>
                </select>
                <p v-if="errors.examType" class="text-sm text-red-600 dark:text-red-400">{{ errors.examType }}</p>
              </div>

              <!-- Subject -->
              <div class="space-y-2">
                <label for="subjectCode" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Subject <span class="text-red-500">*</span>
                </label>
                <select
                  id="subjectCode"
                  v-model="formData.subjectCode"
                  required
                  :class="[
                    'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                    'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                    'dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400',
                    errors.subjectCode
                      ? 'border-red-300 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 bg-white text-gray-900'
                  ]"
                >
                  <option value="">Select subject</option>
                  <option v-for="subject in subjects" :key="subject.id" :value="subject.code">
                    {{ subject.name }} ({{ subject.code }})
                  </option>
                </select>
                <p v-if="errors.subjectCode" class="text-sm text-red-600 dark:text-red-400">{{ errors.subjectCode }}</p>
              </div>

              <!-- Year -->
              <div class="space-y-2">
                <label for="year" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Year <span class="text-red-500">*</span>
                </label>
                <input
                  id="year"
                  v-model.number="formData.year"
                  type="number"
                  required
                  min="2020"
                  max="2030"
                  :class="[
                    'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                    'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                    'dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400',
                    errors.year
                      ? 'border-red-300 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 bg-white text-gray-900'
                  ]"
                  placeholder="Enter year"
                />
                <p v-if="errors.year" class="text-sm text-red-600 dark:text-red-400">{{ errors.year }}</p>
              </div>

              <!-- Grade Level -->
              <div class="space-y-2">
                <label for="gradeLevel" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Grade Level <span class="text-red-500">*</span>
                </label>
                <select
                  id="gradeLevel"
                  v-model="formData.gradeLevel"
                  required
                  :class="[
                    'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                    'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                    'dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400',
                    errors.gradeLevel
                      ? 'border-red-300 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 bg-white text-gray-900'
                  ]"
                >
                  <option value="">Select grade level</option>
                  <option value="A">Grade A</option>
                  <option value="B">Grade B</option>
                  <option value="C">Grade C</option>
                  <option value="D">Grade D</option>
                  <option value="F">Grade F</option>
                </select>
                <p v-if="errors.gradeLevel" class="text-sm text-red-600 dark:text-red-400">{{ errors.gradeLevel }}</p>
              </div>
            </div>
          </div>

          <!-- Score Range Section -->
          <div class="space-y-6">
            <div class="border-l-4 border-blue-500 pl-4">
              <h4 class="text-lg font-semibold text-gray-900 dark:text-white">Score Range</h4>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Define the minimum and maximum scores for this grade</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Minimum Score -->
              <div class="space-y-2">
                <label for="minScore" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Minimum Score <span class="text-red-500">*</span>
                </label>
                <input
                  id="minScore"
                  v-model.number="formData.minScore"
                  type="number"
                  required
                  min="0"
                  max="100"
                  :class="[
                    'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                    'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                    'dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400',
                    errors.minScore
                      ? 'border-red-300 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 bg-white text-gray-900'
                  ]"
                  placeholder="Enter minimum score"
                />
                <p v-if="errors.minScore" class="text-sm text-red-600 dark:text-red-400">{{ errors.minScore }}</p>
              </div>

              <!-- Maximum Score -->
              <div class="space-y-2">
                <label for="maxScore" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Maximum Score <span class="text-red-500">*</span>
                </label>
                <input
                  id="maxScore"
                  v-model.number="formData.maxScore"
                  type="number"
                  required
                  min="0"
                  max="100"
                  :class="[
                    'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                    'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                    'dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400',
                    errors.maxScore
                      ? 'border-red-300 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 bg-white text-gray-900'
                  ]"
                  placeholder="Enter maximum score"
                />
                <p v-if="errors.maxScore" class="text-sm text-red-600 dark:text-red-400">{{ errors.maxScore }}</p>
              </div>
            </div>
          </div>

          <!-- Additional Information Section -->
          <div class="space-y-6">
            <div class="border-l-4 border-green-500 pl-4">
              <h4 class="text-lg font-semibold text-gray-900 dark:text-white">Additional Information</h4>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Optional description and status settings</p>
            </div>

            <div class="space-y-6">
              <!-- Description -->
              <div class="space-y-2">
                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                <textarea
                  id="description"
                  v-model="formData.description"
                  rows="3"
                  class="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 placeholder-gray-500 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400"
                  placeholder="Enter description (optional)"
                />
              </div>

              <!-- Active Status -->
              <div class="flex items-center">
                <input
                  id="isActive"
                  v-model="formData.isActive"
                  type="checkbox"
                  class="h-4 w-4 text-maneb-primary focus:ring-maneb-primary border-gray-300 rounded"
                />
                <label for="isActive" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                  Active (grade boundary is currently in use)
                </label>
              </div>
            </div>
          </div>

          <!-- Enhanced Error Display -->
          <div v-if="submitError" class="bg-red-50 border border-red-200 rounded-lg p-4 dark:bg-red-900/20 dark:border-red-800">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-400">Error updating grade boundary</h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">{{ submitError }}</div>
              </div>
            </div>
          </div>

          <!-- Enhanced Form Actions -->
          <div class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <router-link
              :to="{ name: 'admin.grading-system' }"
              class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary transition-colors duration-200 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600"
            >
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Cancel
            </router-link>
            <button
              type="submit"
              :disabled="isSubmitting"
              class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 shadow-sm"
            >
              <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else class="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
              </svg>
              {{ isSubmitting ? 'Updating Grade Boundary...' : 'Update Grade Boundary' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useGradingStore, useGradingApiStore } from '@/stores'
import { useExamTypesAutoLoad } from '@/composables/useExamTypes'
import type { UpdateGradeBoundaryRequest, GradeBoundaryDto as ExternalGradeBoundaryDto } from '@/interfaces/external/results-api.interface'
import type { GradeLevel } from '@/interfaces'

// Define the interface for the grade boundary we're working with
interface GradeBoundaryDto {
  id: string
  examLevel: string
  subjectCode: string
  grade: string
  lowerBound: number
  upperBound: number
  isActive: boolean
  examYear: string
  cohort: string
  award: string
  gradeValue: number
  // Additional properties for display
  year?: number
  examType?: string
  subjectId?: string
  minScore?: number
  maxScore?: number
  gradeLevel?: string
  description?: string
}

import sweetAlert from '@/utils/ui/sweetAlert'

// Router
const router = useRouter()
const route = useRoute()

// Stores
const gradingStore = useGradingStore()
const gradingApiStore = useGradingApiStore()

// Load exam types from API using composable
const { examTypesLocal: examTypes } = useExamTypesAutoLoad()

// Reactive state
const gradeBoundary = ref<GradeBoundaryDto | null>(null)
const isLoading = ref(false)
const loadError = ref<string | null>(null)
const isSubmitting = ref(false)
const submitError = ref<string | null>(null)
const subjects = ref<any[]>([])

const formData = reactive<{
  examType: string
  subjectCode: string
  year: number
  gradeLevel: string
  minScore: number | null
  maxScore: number | null
  description: string
  isActive: boolean
}>({
  examType: '',
  subjectCode: '',
  year: new Date().getFullYear(),
  gradeLevel: '',
  minScore: null,
  maxScore: null,
  description: '',
  isActive: true
})

const errors = reactive<Record<string, string>>({})

// Computed
const gradeBoundaryId = computed(() => route.params.id as string)

// Methods
const getSubjectName = (subjectId: string): string => {
  const subject = subjects.value.find(s => s.id === subjectId)
  return subject ? subject.name : 'Unknown Subject'
}

const populateForm = (boundary: GradeBoundaryDto) => {
  // Map external API properties to form data
  formData.examType = boundary.examLevel || boundary.examType || ''
  formData.subjectCode = boundary.subjectCode || ''
  formData.year = boundary.year || parseInt(boundary.examYear) || new Date().getFullYear()
  formData.gradeLevel = boundary.grade || boundary.gradeLevel || ''
  formData.minScore = boundary.lowerBound || boundary.minScore || null
  formData.maxScore = boundary.upperBound || boundary.maxScore || null
  formData.description = boundary.description || ''
  formData.isActive = boundary.isActive ?? true
}

const validateForm = (): boolean => {
  // Clear previous errors
  Object.keys(errors).forEach(key => delete errors[key])

  let isValid = true

  // Required field validation
  if (!formData.examType.trim()) {
    errors.examType = 'Exam type is required'
    isValid = false
  }

  if (!formData.subjectCode.trim()) {
    errors.subjectCode = 'Subject is required'
    isValid = false
  }

  if (!formData.year) {
    errors.year = 'Year is required'
    isValid = false
  } else if (formData.year < 2020 || formData.year > 2030) {
    errors.year = 'Year must be between 2020 and 2030'
    isValid = false
  }

  if (!formData.gradeLevel) {
    errors.gradeLevel = 'Grade level is required'
    isValid = false
  }

  if (formData.minScore === null || formData.minScore === undefined) {
    errors.minScore = 'Minimum score is required'
    isValid = false
  } else if (formData.minScore < 0 || formData.minScore > 100) {
    errors.minScore = 'Minimum score must be between 0 and 100'
    isValid = false
  }

  if (formData.maxScore === null || formData.maxScore === undefined) {
    errors.maxScore = 'Maximum score is required'
    isValid = false
  } else if (formData.maxScore < 0 || formData.maxScore > 100) {
    errors.maxScore = 'Maximum score must be between 0 and 100'
    isValid = false
  }

  // Validate score range
  if (formData.minScore !== null && formData.maxScore !== null && formData.minScore >= formData.maxScore) {
    errors.maxScore = 'Maximum score must be greater than minimum score'
    isValid = false
  }

  return isValid
}

const handleSubmit = async () => {
  if (!validateForm() || !gradeBoundary.value?.id) {
    return
  }

  try {
    isSubmitting.value = true
    submitError.value = null

    // Find the subject ID from the subject code
    const subject = subjects.value.find(s => s.code === formData.subjectCode)
    if (!subject) {
      throw new Error('Selected subject not found')
    }

    // Prepare data for submission
    const submitData: UpdateGradeBoundaryRequest = {
      examType: formData.examType,
      subjectId: subject.id,
      year: formData.year,
      gradeLevel: formData.gradeLevel as GradeLevel,
      minScore: formData.minScore!,
      maxScore: formData.maxScore!,
      description: formData.description || undefined,
      isActive: formData.isActive
    }

    // Submit to grading API
    await gradingApiStore.updateGradeBoundary(gradeBoundary.value.id, submitData)

    // Show success message
    await sweetAlert.success('Grade boundary updated successfully!')

    // Navigate back to grading system
    router.push({ name: 'admin.grading-system' })

  } catch (error: any) {
    submitError.value = error.message || 'Failed to update grade boundary'
    console.error('Error updating grade boundary:', error)
  } finally {
    isSubmitting.value = false
  }
}

const fetchGradeBoundaryData = async () => {
  try {
    isLoading.value = true
    loadError.value = null

    // Fetch the grade boundary data
    const boundary = await gradingApiStore.getGradeBoundary(gradeBoundaryId.value)
    gradeBoundary.value = boundary

    // Populate the form with the boundary data
    populateForm(boundary)

  } catch (error: any) {
    loadError.value = error.message || 'Failed to load grade boundary'
    console.error('Error loading grade boundary:', error)
  } finally {
    isLoading.value = false
  }
}

const fetchSubjects = async () => {
  try {
    await gradingStore.fetchSubjects()
    subjects.value = gradingStore.subjects
  } catch (error) {
    console.error('Error fetching subjects:', error)
  }
}

// Lifecycle
onMounted(async () => {
  await fetchSubjects()
  await fetchGradeBoundaryData()
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary-dark:hover {
  background-color: #8b2424;
}
</style>
