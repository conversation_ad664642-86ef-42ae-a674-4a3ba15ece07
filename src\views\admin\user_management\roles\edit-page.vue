<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-maneb-primary"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="loadError" class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error loading role</h3>
          <p class="mt-1 text-sm text-red-700">{{ loadError }}</p>
        </div>
      </div>
    </div>

    <!-- Edit Role Content -->
    <div v-else-if="role" class="space-y-6">
      <!-- Breadcrumbs -->
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <router-link to="/admin" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-maneb-primary">
              Admin
            </router-link>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <router-link to="/admin/user-management" class="ml-1 text-sm font-medium text-gray-700 hover:text-maneb-primary md:ml-2">User Management</router-link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <router-link to="/admin/user-management/roles" class="ml-1 text-sm font-medium text-gray-700 hover:text-maneb-primary md:ml-2">Roles</router-link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <router-link :to="{ name: 'admin.user-management.roles.detail', params: { id: role.id } }" class="ml-1 text-sm font-medium text-gray-700 hover:text-maneb-primary md:ml-2">{{ role.name }}</router-link>
            </div>
          </li>
          <li aria-current="page">
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Edit</span>
            </div>
          </li>
        </ol>
      </nav>

      <!-- Header -->
      <div class="mb-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Role: {{ role.name }}</h1>
            <p class="mt-1 text-sm text-gray-600">Update role information and permissions</p>
          </div>
          <div class="flex space-x-3">
            <router-link
              :to="{ name: 'admin.user-management.roles.detail', params: { id: role.id } }"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
            >
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              View Details
            </router-link>
            <router-link
              :to="{ name: 'admin.user-management.roles' }"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
            >
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Roles
            </router-link>
          </div>
        </div>
      </div>

      <!-- Submit Error -->
      <div v-if="submitError" class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Error updating role</h3>
            <p class="mt-1 text-sm text-red-700">{{ submitError }}</p>
          </div>
        </div>
      </div>

      <!-- Edit Role Form -->
      <div class="bg-white shadow-sm border border-gray-200 rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-black">Role Information</h3>
        </div>
        
        <form @submit.prevent="handleSubmit" class="px-6 py-4 space-y-6">
          <!-- Role Name -->
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700">
              Role Name <span class="text-red-500">*</span>
            </label>
            <input
              id="name"
              v-model="formData.name"
              type="text"
              required
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
              placeholder="Enter role name"
            />
            <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</p>
          </div>

          <!-- Description -->
          <div>
            <label for="description" class="block text-sm font-medium text-gray-700">
              Description
            </label>
            <textarea
              id="description"
              v-model="formData.description"
              rows="3"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
              placeholder="Enter role description"
            ></textarea>
            <p v-if="errors.description" class="mt-1 text-sm text-red-600">{{ errors.description }}</p>
          </div>

          <!-- Status -->
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700">
              Status <span class="text-red-500">*</span>
            </label>
            <select
              id="status"
              v-model="formData.status"
              required
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
            >
              <option value="">Select status...</option>
              <option value="Unapproved">Unapproved</option>
              <option value="Approved">Approved</option>
              <option value="SecondApproved">Second Approved</option>
              <option value="Rejected">Rejected</option>
            </select>
            <p v-if="errors.status" class="mt-1 text-sm text-red-600">{{ errors.status }}</p>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <router-link
              :to="{ name: 'admin.user-management.roles.detail', params: { id: role.id } }"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
            >
              Cancel
            </router-link>
            <button
              type="submit"
              :disabled="isSubmitting"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isSubmitting ? 'Updating...' : 'Update Role' }}
            </button>
          </div>
        </form>
      </div>

      <!-- Module Permissions Section -->
      <div v-if="!isSystemAdminRole" class="bg-white shadow-sm border border-gray-200 rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-medium text-black">Module Permissions</h3>
              <p class="mt-1 text-sm text-gray-600">
                Configure which system modules this role can access and what actions they can perform.
              </p>
            </div>
            <div class="flex items-center space-x-4">
              <div class="text-right">
                <div class="text-sm font-medium text-maneb-primary">
                  {{ totalSelectedPermissions }} permission{{ totalSelectedPermissions !== 1 ? 's' : '' }}
                </div>
                <div class="text-xs text-gray-500">
                  {{ selectedModulesCount }} of {{ availableModules.length }} modules
                </div>
              </div>
              <div class="flex space-x-2">
                <button
                  @click="selectAllPermissions"
                  type="button"
                  class="inline-flex items-center px-3 py-1.5 border border-maneb-primary text-xs font-medium rounded text-maneb-primary hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
                >
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Select All
                </button>
                <button
                  @click="clearAllPermissions"
                  type="button"
                  class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  Clear All
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="p-6">
          <div v-if="isLoadingPermissions" class="flex justify-center py-12">
            <div class="text-center">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-maneb-primary mx-auto"></div>
              <p class="mt-2 text-sm text-gray-500">Loading permissions...</p>
            </div>
          </div>

          <div v-else-if="availableModules.length === 0" class="text-center py-12">
            <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
              </svg>
            </div>
            <h3 class="text-sm font-medium text-gray-900 mb-1">No Modules Available</h3>
            <p class="text-sm text-gray-500">No system modules are currently available for permission assignment.</p>
          </div>

          <div v-else class="space-y-3">
            <!-- Main Modules (Level 0) -->
            <div v-for="module in mainModules" :key="module.key" class="border border-gray-200 rounded-lg overflow-hidden">
              <!-- Module Header -->
              <div
                @click="toggleModuleExpansion(module.key)"
                class="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                :class="{ 'bg-red-50': hasAnyPermissionInModule(module.key) }"
              >
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 rounded-lg flex items-center justify-center"
                       :class="hasAnyPermissionInModule(module.key) ? 'bg-red-100' : 'bg-gray-100'">
                    <component :is="getModuleIcon(module.key)"
                               class="w-5 h-5"
                               :class="hasAnyPermissionInModule(module.key) ? 'text-maneb-primary' : 'text-gray-500'" />
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center space-x-2">
                      <h4 class="text-sm font-medium text-black">{{ module.name }}</h4>
                      <span v-if="hasAnyPermissionInModule(module.key)"
                            class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-maneb-primary">
                        Active
                      </span>
                    </div>
                    <p class="text-xs text-gray-500 mt-0.5">{{ module.description || 'No description available' }}</p>
                    <div class="flex items-center space-x-4 mt-1">
                      <span class="text-xs text-gray-500">
                        {{ getSelectedActionsCount(module.key) }} of {{ module.actions.length }} actions
                      </span>
                      <span v-if="getSubModules(module.key).length > 0" class="text-xs text-gray-500">
                        {{ getSubModules(module.key).length }} sub-modules
                      </span>
                    </div>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <div v-if="getSelectedActionsCount(module.key) > 0"
                       class="w-2 h-2 bg-maneb-primary rounded-full"></div>
                  <svg
                    :class="{ 'rotate-180': expandedModules.has(module.key) }"
                    class="w-5 h-5 text-gray-400 transition-transform"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>

              <!-- Module Content -->
              <div v-if="expandedModules.has(module.key)" class="border-t border-gray-200">
                <!-- Module Actions -->
                <div class="p-4 bg-gray-50">
                  <div class="mb-3">
                    <h5 class="text-xs font-medium text-gray-700 uppercase tracking-wide mb-2">Module Actions</h5>
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
                      <div v-for="action in module.actions" :key="`${module.key}-${action.key}`"
                           class="flex items-center">
                        <input
                          :id="`${module.key}-${action.key}`"
                          type="checkbox"
                          :checked="isPermissionSelected(module.key, action.key)"
                          @change="handlePermissionChange(module.key, action.key, $event)"
                          class="h-4 w-4 text-maneb-primary focus:ring-maneb-primary border-gray-300 rounded"
                        />
                        <label :for="`${module.key}-${action.key}`" class="ml-2 text-sm text-gray-700">
                          {{ action.name }}
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Sub-modules -->
                <div v-if="getSubModules(module.key).length > 0" class="border-t border-gray-200">
                  <div class="p-4">
                    <h5 class="text-xs font-medium text-gray-700 uppercase tracking-wide mb-3">Sub-modules</h5>
                    <div class="space-y-3">
                      <div v-for="subModule in getSubModules(module.key)" :key="subModule.key"
                           class="border border-gray-200 rounded-md">
                        <div class="p-3 bg-white">
                          <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-2">
                              <div class="w-6 h-6 bg-gray-100 rounded flex items-center justify-center">
                                <svg class="w-3 h-3 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                                </svg>
                              </div>
                              <div>
                                <h6 class="text-sm font-medium text-black">{{ subModule.name }}</h6>
                                <p class="text-xs text-gray-500">{{ subModule.description || 'No description available' }}</p>
                              </div>
                            </div>
                            <span class="text-xs text-gray-500">
                              {{ getSelectedActionsCount(subModule.key) }}/{{ subModule.actions.length }}
                            </span>
                          </div>
                          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
                            <div v-for="action in subModule.actions" :key="`${subModule.key}-${action.key}`"
                                 class="flex items-center">
                              <input
                                :id="`${subModule.key}-${action.key}`"
                                type="checkbox"
                                :checked="isPermissionSelected(subModule.key, action.key)"
                                @change="handlePermissionChange(subModule.key, action.key, $event)"
                                class="h-3 w-3 text-maneb-primary focus:ring-maneb-primary border-gray-300 rounded"
                              />
                              <label :for="`${subModule.key}-${action.key}`" class="ml-2 text-xs text-gray-700">
                                {{ action.name }}
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Save Permissions Button -->
          <div class="flex justify-between items-center pt-6 border-t border-gray-200 mt-6">
            <div class="text-sm text-gray-500">
              <span class="font-medium">{{ totalSelectedPermissions }}</span> permissions selected across
              <span class="font-medium">{{ selectedModulesCount }}</span> modules
            </div>
            <button
              @click="savePermissions"
              :disabled="isSubmittingPermissions"
              type="button"
              class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <svg v-if="isSubmittingPermissions" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              {{ isSubmittingPermissions ? 'Saving Permissions...' : 'Save Permissions' }}
            </button>
          </div>
        </div>
      </div>

      <!-- System Admin Notice -->
      <div v-else class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">System Administrator Role</h3>
            <p class="mt-1 text-sm text-blue-700">
              This is a system administrator role with full access to all modules and actions. Permissions cannot be modified.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useRoleStore } from '@/stores/management/role.store'
import { useSectionStore } from '@/stores/management/section.store'
import { sweetAlert, showSuccessAlert, showErrorAlert } from '@/utils/ui/sweetAlert'
import type { RoleDto, UpdateRoleRequest, RecordStatus, PermissionAction, RolePermissionDto } from '@/interfaces'

// Router
const route = useRoute()
const router = useRouter()

// Stores
const roleStore = useRoleStore()
const sectionStore = useSectionStore()

// Reactive state
const role = ref<RoleDto | null>(null)
const isLoading = ref(false)
const loadError = ref<string | null>(null)
const isSubmitting = ref(false)
const submitError = ref<string | null>(null)

// Permissions state
const isLoadingPermissions = ref(false)
const isSubmittingPermissions = ref(false)
const selectedPermissions = ref<Map<string, Set<string>>>(new Map())
const expandedModules = ref<Set<string>>(new Set())
const availableModules = ref<Array<{
  key: string;
  name: string;
  description?: string;
  controller?: string;
  area?: string;
  level?: number;
  parentSectionId?: string;
  isActive?: boolean;
  actions: Array<{ key: string; name: string }>
}>>([])

// Available actions for permissions
const availableActions = [
  { key: 'Create', name: 'Create' },
  { key: 'Read', name: 'View' },
  { key: 'Update', name: 'Update' },
  { key: 'Delete', name: 'Delete' },
  { key: 'All', name: 'All Actions' }
]

const formData = reactive<UpdateRoleRequest>({
  name: '',
  description: '',
  status: 'Unapproved' as RecordStatus
})

const errors = reactive<Record<string, string>>({})

// Computed
const roleId = computed(() => route.params.id as string)

const isSystemAdminRole = computed(() => {
  if (!role.value) return false
  const roleName = role.value.name?.toLowerCase() || ''
  const systemAdminVariations = [
    'system administrator',
    'system admin',
    'sys admin'
  ]
  return systemAdminVariations.some(variation => roleName.includes(variation.toLowerCase()))
})

const totalSelectedPermissions = computed(() => {
  let total = 0
  selectedPermissions.value.forEach(actions => {
    total += actions.size
  })
  return total
})

const selectedModulesCount = computed(() => {
  let count = 0
  selectedPermissions.value.forEach(actions => {
    if (actions.size > 0) count++
  })
  return count
})

const mainModules = computed(() => {
  return availableModules.value.filter(module => module.level === 0)
})

const getSubModules = (parentKey: string) => {
  return availableModules.value.filter(module => module.parentSectionId === parentKey)
}

const hasAnyPermissionInModule = (moduleKey: string): boolean => {
  // Check main module permissions
  const modulePermissions = selectedPermissions.value.get(moduleKey)
  if (modulePermissions && modulePermissions.size > 0) {
    return true
  }

  // Check sub-module permissions
  const subModules = getSubModules(moduleKey)
  return subModules.some(subModule => {
    const subModulePermissions = selectedPermissions.value.get(subModule.key)
    return subModulePermissions && subModulePermissions.size > 0
  })
}

const getModuleIcon = (_moduleKey: string) => {
  // Return a simple SVG component name for now
  // In a real implementation you'd use proper Vue components or icon libraries
  return 'svg'
}

// Methods
const validateForm = (): boolean => {
  // Clear previous errors
  Object.keys(errors).forEach(key => delete errors[key])

  let isValid = true

  if (!formData.name?.trim()) {
    errors.name = 'Role name is required'
    isValid = false
  }

  if (!formData.status) {
    errors.status = 'Status is required'
    isValid = false
  }

  return isValid
}

const handleSubmit = async () => {
  if (!validateForm() || !role.value?.id) {
    return
  }

  try {
    isSubmitting.value = true
    submitError.value = null
    
    await roleStore.updateRole(role.value.id, formData)
    
    await showSuccessAlert('Role updated successfully!')
    
    router.push({ name: 'admin.user-management.roles.detail', params: { id: role.value.id } })
  } catch (error: any) {
    submitError.value = error.message || 'Failed to update role'
    console.error('Error updating role:', error)
    await sweetAlert.error('Failed to Update Role', error.message || 'An error occurred while updating the role. Please try again.')
  } finally {
    isSubmitting.value = false
  }
}

const fetchRoleDetails = async () => {
  if (!roleId.value) {
    loadError.value = 'No role ID provided'
    return
  }

  try {
    isLoading.value = true
    loadError.value = null
    role.value = await roleStore.fetchRoleById(roleId.value)

    console.log('Fetched role details:', role.value)
    console.log('Role permissions:', role.value?.rolePermissions)
    console.log('Role permissions length:', role.value?.rolePermissions?.length || 0)

    // Populate form data
    if (role.value) {
      formData.name = role.value.name || ''
      formData.description = role.value.description || ''
      formData.status = role.value.status || 'Unapproved'
    }
  } catch (err: any) {
    loadError.value = err.message || 'Failed to load role details'
    console.error('Error fetching role details:', err)
  } finally {
    isLoading.value = false
  }
}

// Permissions Methods
const loadSections = async () => {
  try {
    await sectionStore.fetchSections()
    const sections = sectionStore.sections

    console.log('Loaded sections:', sections)
    console.log('Sections count:', sections?.length || 0)

    // Convert sections to modules with enhanced structure
    availableModules.value = sections.map((section: any) => ({
      key: section.id || section.name,
      name: section.name,
      description: section.description,
      controller: section.controller,
      area: section.area,
      level: section.level || 0,
      parentSectionId: section.parentSectionId,
      isActive: section.isActive !== false,
      actions: availableActions
    }))

    console.log('Available modules:', availableModules.value)

    // Auto-expand main modules (level 0) that have permissions
    availableModules.value.forEach(module => {
      if (module.level === 0 && hasAnyPermissionInModule(module.key)) {
        expandedModules.value.add(module.key)
      }
    })

    // If no modules are expanded, expand the first main module
    if (expandedModules.value.size === 0 && mainModules.value.length > 0) {
      expandedModules.value.add(mainModules.value[0].key)
    }
  } catch (error) {
    console.error('Error loading sections:', error)
    await showErrorAlert('Failed to load system modules. Please try again.')
  }
}

const loadRolePermissions = async () => {
  if (!roleId.value || !role.value) {
    selectedPermissions.value.clear()
    return
  }

  try {
    isLoadingPermissions.value = true
    console.log(`Loading permissions for role: ${roleId.value}`)
    console.log('Role object:', role.value)

    // Clear existing selections
    selectedPermissions.value.clear()

    // Check if role has permissions (the API returns role with permissions)
    if (role.value.rolePermissions && Array.isArray(role.value.rolePermissions) && role.value.rolePermissions.length > 0) {
      console.log(`Found ${role.value.rolePermissions.length} existing permissions in role object`)

      role.value.rolePermissions.forEach((permission: RolePermissionDto) => {
        console.log('Processing permission:', permission)

        // Use sectionID as the module key
        const moduleKey = permission.sectionID
        if (!moduleKey) {
          console.warn('Permission missing sectionID:', permission)
          return
        }

        // Initialize module permissions if not exists
        if (!selectedPermissions.value.has(moduleKey)) {
          selectedPermissions.value.set(moduleKey, new Set())
        }

        // Add permission if canAccess is true
        if (permission.canAccess && permission.action) {
          selectedPermissions.value.get(moduleKey)?.add(permission.action)
          console.log(`Added permission: ${moduleKey}:${permission.action}`)
        }
      })
    } else {
      console.log('No rolePermissions found in role object or rolePermissions is not an array')
      console.log('rolePermissions value:', role.value.rolePermissions)
    }

    console.log('Final selected permissions:', selectedPermissions.value)
  } catch (error) {
    console.error('Error loading role permissions:', error)
    await showErrorAlert('Failed to load role permissions. Please try again.')
  } finally {
    isLoadingPermissions.value = false
  }
}

const toggleModuleExpansion = (moduleKey: string) => {
  if (expandedModules.value.has(moduleKey)) {
    expandedModules.value.delete(moduleKey)
  } else {
    expandedModules.value.add(moduleKey)
  }
}

const isPermissionSelected = (moduleKey: string, actionKey: string): boolean => {
  return selectedPermissions.value.get(moduleKey)?.has(actionKey) || false
}

const handlePermissionChange = (moduleKey: string, actionKey: string, event: Event) => {
  const target = event.target as HTMLInputElement
  togglePermission(moduleKey, actionKey, target.checked)
}

const togglePermission = (moduleKey: string, actionKey: string, isSelected: boolean) => {
  console.log(`🔄 Toggling permission: ${moduleKey}:${actionKey} = ${isSelected}`)

  if (!selectedPermissions.value.has(moduleKey)) {
    selectedPermissions.value.set(moduleKey, new Set())
  }

  const modulePermissions = selectedPermissions.value.get(moduleKey)!
  if (isSelected) {
    modulePermissions.add(actionKey)
    console.log(`✅ Added permission: ${moduleKey}:${actionKey}`)
  } else {
    modulePermissions.delete(actionKey)
    console.log(`❌ Removed permission: ${moduleKey}:${actionKey}`)
  }
}

const selectAllPermissions = () => {
  availableModules.value.forEach(module => {
    const modulePermissions = new Set<string>()
    module.actions.forEach(action => {
      modulePermissions.add(action.key)
    })
    selectedPermissions.value.set(module.key, modulePermissions)
  })
}

const clearAllPermissions = () => {
  selectedPermissions.value.clear()
}

const getSelectedActionsCount = (moduleKey: string): number => {
  return selectedPermissions.value.get(moduleKey)?.size || 0
}

const savePermissions = async () => {
  if (!roleId.value || !role.value) {
    await showErrorAlert('Role information not available.')
    return
  }

  if (selectedPermissions.value.size === 0) {
    await showErrorAlert('Please select at least one permission before saving.')
    return
  }

  try {
    isSubmittingPermissions.value = true

    // Prepare permissions data
    const permissions: Array<{ sectionId: string; action: PermissionAction; canAccess: boolean }> = []

    selectedPermissions.value.forEach((actions, moduleKey) => {
      actions.forEach(action => {
        if (moduleKey && action && moduleKey.trim() !== '' && action.trim() !== '') {
          permissions.push({
            sectionId: moduleKey.trim(),
            action: action.trim() as PermissionAction,
            canAccess: true
          })
        }
      })
    })

    if (permissions.length === 0) {
      await showErrorAlert('No valid permissions found to save.')
      return
    }

    console.log(`Saving ${permissions.length} permissions for role ${role.value.name}`)

    // Update role with new permissions
    await roleStore.updateRoleWithPermissions(roleId.value, permissions)

    await showSuccessAlert(
      'Permissions Saved Successfully!',
      `${permissions.length} permissions have been assigned to role "${role.value.name}".`
    )

    // Reload the role permissions to reflect changes
    await loadRolePermissions()

  } catch (error: any) {
    console.error('Error saving permissions:', error)

    let errorMessage = 'Failed to save permissions. Please try again.'
    if (error.message) {
      if (error.message.includes('Bulk creation failed')) {
        errorMessage = `Permission assignment partially failed: ${error.message}`
      } else {
        errorMessage = `Failed to save permissions: ${error.message}`
      }
    }

    await showErrorAlert(errorMessage)
  } finally {
    isSubmittingPermissions.value = false
  }
}

// Watch for role changes to reload permissions
watch(role, async (newRole) => {
  if (newRole && newRole.id) {
    console.log('Role changed, reloading permissions for:', newRole.name)
    await loadRolePermissions()
  }
}, { immediate: false })

// Lifecycle
onMounted(async () => {
  try {
    // First load sections and role details in parallel
    await Promise.all([
      fetchRoleDetails(),
      loadSections()
    ])

    // Then load permissions after role details are available
    if (role.value) {
      await loadRolePermissions()
    }
  } catch (error) {
    console.error('Error during component initialization:', error)
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>
