/**
 * @fileoverview Centralized Exam Types Configuration for MANEB System
 * @description Single source of truth for all exam type definitions and utilities
 * This ensures uniform exam type options across all components
 */

import { examTypeService, type ExamTypeDto, type ExamTypeOption as ApiExamTypeOption } from '@/services/exam-type.service';

/**
 * Basic exam type option interface
 */
export interface ExamTypeOption {
  value: string;
  label: string;
  description: string;
}

/**
 * Extended exam type information with additional metadata
 */
export interface ExamTypeInfo extends ExamTypeOption {
  /** Duration of the exam in years */
  duration?: number;
  /** Target age range for the exam */
  targetAge?: string;
  /** Whether this exam type is currently active */
  isActive?: boolean;
  /** Display order for UI components */
  displayOrder?: number;
}

/**
 * Filter option for hierarchical filtering
 */
export interface FilterOption {
  id: string;
  name: string;
  parentId?: string;
}

/**
 * Legacy exam types for backward compatibility
 * @deprecated Use examTypeService.fetchExamTypes() instead
 * @description Complete exam type definitions with metadata
 */
export const EXAM_TYPES: ExamTypeInfo[] = [
  {
    value: 'PLCE',
    label: 'PLCE',
    description: 'Primary Leaving Certificate Examination',
    duration: 8,
    targetAge: '13-15 years',
    isActive: true,
    displayOrder: 1
  },
  {
    value: 'JCE',
    label: 'JCE',
    description: 'Junior Certificate of Education',
    duration: 2,
    targetAge: '15-17 years',
    isActive: true,
    displayOrder: 2
  },
  {
    value: 'MSCE',
    label: 'MSCE',
    description: 'Malawi School Certificate of Education',
    duration: 4,
    targetAge: '17-19 years',
    isActive: true,
    displayOrder: 3
  },
  {
    value: 'TTC',
    label: 'TTC',
    description: 'Teacher Training Certificate',
    duration: 2,
    targetAge: '19+ years',
    isActive: true,
    displayOrder: 4
  },
  {
    value: 'PSLCE',
    label: 'PSLCE',
    description: 'Primary School Leaving Certificate Examination',
    duration: 8,
    targetAge: '13-15 years',
    isActive: false, // Legacy exam type
    displayOrder: 5
  }
]

/**
 * Get exam type values only (for type unions and validation)
 */
export const EXAM_TYPE_VALUES = ['PLCE', 'JCE', 'MSCE', 'TTC', 'PSLCE'] as const;

/**
 * Type for exam type values
 */
export type ExamTypeValue = typeof EXAM_TYPE_VALUES[number];

/**
 * Get active exam types only
 */
export const getActiveExamTypes = (): ExamTypeInfo[] => {
  return EXAM_TYPES.filter(type => type.isActive !== false)
    .sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0));
};

/**
 * Get exam types as simple options for dropdowns
 */
export const getExamTypeOptions = (activeOnly: boolean = true): ExamTypeOption[] => {
  const types = activeOnly ? getActiveExamTypes() : EXAM_TYPES;
  return types.map(({ value, label, description }) => ({ value, label, description }));
};

/**
 * Helper function to get exam type description by value
 */
export const getExamTypeDescription = (value: string): string => {
  const examType = EXAM_TYPES.find(type => type.value === value);
  return examType?.description || value;
};

/**
 * Helper function to get exam type label by value
 */
export const getExamTypeLabel = (value: string): string => {
  const examType = EXAM_TYPES.find(type => type.value === value);
  return examType?.label || value;
};

/**
 * Helper function to get full exam type display text
 */
export const getExamTypeDisplay = (value: string): string => {
  const examType = EXAM_TYPES.find(type => type.value === value);
  return examType ? `${examType.label} (${examType.description})` : value;
};

/**
 * Helper function to get exam type info by value
 */
export const getExamTypeInfo = (value: string): ExamTypeInfo | undefined => {
  return EXAM_TYPES.find(type => type.value === value);
};

/**
 * Validate if a value is a valid exam type
 */
export const isValidExamType = (value: string): value is ExamTypeValue => {
  return EXAM_TYPE_VALUES.includes(value as ExamTypeValue);
};

/**
 * Generate exam type filter options for UI components
 */
export const generateExamTypeFilterOptions = (parentId?: string): Array<{id: string, name: string, parentId?: string}> => {
  return getActiveExamTypes().map((examType, index) => ({
    id: `exam_type_${index + 1}`,
    name: getExamTypeDisplay(examType.value),
    parentId: parentId
  }));
};

// ============================================================================
// API-BASED EXAM TYPE FUNCTIONS
// ============================================================================

/**
 * Get exam types from API
 * @description Fetches exam types from the /ExamType endpoint
 */
export const fetchExamTypesFromApi = async (forceRefresh: boolean = false): Promise<ExamTypeDto[]> => {
  return examTypeService.fetchExamTypes(forceRefresh);
};

/**
 * Get active exam types from API
 */
export const getActiveExamTypesFromApi = async (): Promise<ExamTypeDto[]> => {
  return examTypeService.getActiveExamTypes();
};

/**
 * Get exam type options from API for dropdowns
 */
export const getExamTypeOptionsFromApi = async (activeOnly: boolean = true): Promise<ApiExamTypeOption[]> => {
  return examTypeService.getExamTypeOptions(activeOnly);
};

/**
 * Get exam type description from API by ID
 */
export const getExamTypeDescriptionFromApi = async (id: string): Promise<string> => {
  return examTypeService.getExamTypeDescription(id);
};

/**
 * Check if exam type ID is valid using API
 */
export const isValidExamTypeFromApi = async (id: string): Promise<boolean> => {
  return examTypeService.isValidExamType(id);
};

/**
 * Generate exam type filter options from API for UI components
 */
export const generateExamTypeFilterOptionsFromApi = async (parentId?: string): Promise<FilterOption[]> => {
  return examTypeService.getFilterOptions(parentId);
};

/**
 * Convert API exam type to local ExamTypeInfo format
 */
export const convertApiExamTypeToLocal = (apiExamType: ExamTypeDto): ExamTypeInfo => {
  return {
    value: apiExamType.id,
    label: apiExamType.id,
    description: apiExamType.name,
    isActive: apiExamType.isActive,
    displayOrder: apiExamType.numericId
  };
};

/**
 * Get exam types from API in local format
 */
export const getExamTypesFromApi = async (activeOnly: boolean = true): Promise<ExamTypeInfo[]> => {
  const apiExamTypes = activeOnly ? await getActiveExamTypesFromApi() : await fetchExamTypesFromApi();
  return apiExamTypes.map(convertApiExamTypeToLocal);
};
