<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-maneb-primary"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="loadError" class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error loading role</h3>
          <p class="mt-1 text-sm text-red-700">{{ loadError }}</p>
        </div>
      </div>
    </div>

    <!-- Role Status Management Content -->
    <div v-else-if="role" class="space-y-6">
      <!-- Breadcrumbs -->
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <router-link to="/admin" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-maneb-primary">
              Admin
            </router-link>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <router-link to="/admin/user-management" class="ml-1 text-sm font-medium text-gray-700 hover:text-maneb-primary md:ml-2">User Management</router-link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <router-link to="/admin/user-management/roles" class="ml-1 text-sm font-medium text-gray-700 hover:text-maneb-primary md:ml-2">Roles</router-link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <router-link :to="{ name: 'admin.user-management.roles.detail', params: { id: role.id } }" class="ml-1 text-sm font-medium text-gray-700 hover:text-maneb-primary md:ml-2">{{ role.name }}</router-link>
            </div>
          </li>
          <li aria-current="page">
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Manage Status</span>
            </div>
          </li>
        </ol>
      </nav>

      <!-- Header -->
      <div class="mb-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Manage Role Status</h1>
            <p class="mt-1 text-sm text-gray-600">Update the approval status for {{ role.name }}</p>
          </div>
          <div class="flex space-x-3">
            <router-link
              :to="{ name: 'admin.user-management.roles.detail', params: { id: role.id } }"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
            >
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              View Details
            </router-link>
            <router-link
              :to="{ name: 'admin.user-management.roles' }"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
            >
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Roles
            </router-link>
          </div>
        </div>
      </div>

      <!-- Current Status Card -->
      <div class="bg-white shadow-sm border border-gray-200 rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-black">Current Status</h3>
        </div>
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-900">{{ role.name }}</p>
              <p class="text-sm text-gray-500">Role ID: {{ role.id }}</p>
            </div>
            <span :class="getStatusBadgeClass(role.status)" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium">
              {{ role.status }}
            </span>
          </div>
        </div>
      </div>

      <!-- Submit Error -->
      <div v-if="submitError" class="bg-red-50 border border-red-200 rounded-md p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Error updating status</h3>
            <p class="mt-1 text-sm text-red-700">{{ submitError }}</p>
          </div>
        </div>
      </div>

      <!-- Status Update Form -->
      <div class="bg-white shadow-sm border border-gray-200 rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-black">Update Status</h3>
        </div>
        
        <form @submit.prevent="handleStatusUpdate" class="px-6 py-4 space-y-6">
          <!-- New Status -->
          <div>
            <label for="newStatus" class="block text-sm font-medium text-gray-700">
              New Status <span class="text-red-500">*</span>
            </label>
            <select
              id="newStatus"
              v-model="selectedStatus"
              required
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
            >
              <option value="">Choose new status...</option>
              <option value="Unapproved">Unapproved</option>
              <option value="Approved">Approved</option>
              <option value="SecondApproved">Second Approved</option>
              <option value="Rejected">Rejected</option>
            </select>
            <p v-if="errors.status" class="mt-1 text-sm text-red-600">{{ errors.status }}</p>
          </div>

          <!-- Reason -->
          <div>
            <label for="reason" class="block text-sm font-medium text-gray-700">
              Reason for Status Change <span class="text-red-500">*</span>
            </label>
            <textarea
              id="reason"
              v-model="reason"
              rows="3"
              required
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
              placeholder="Please provide a reason for this status change..."
            ></textarea>
            <p v-if="errors.reason" class="mt-1 text-sm text-red-600">{{ errors.reason }}</p>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <router-link
              :to="{ name: 'admin.user-management.roles.detail', params: { id: role.id } }"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
            >
              Cancel
            </router-link>
            <button
              type="submit"
              :disabled="isSubmitting"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isSubmitting ? 'Updating...' : 'Update Status' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useRoleStore } from '@/store/role.store'
import { showSuccessAlert, showConfirmDialog } from '@/utils/sweetAlert'
import type { RoleDto, RecordStatus } from '@/interfaces'

// Router
const route = useRoute()
const router = useRouter()

// Store
const roleStore = useRoleStore()

// Reactive state
const role = ref<RoleDto | null>(null)
const isLoading = ref(false)
const loadError = ref<string | null>(null)
const isSubmitting = ref(false)
const submitError = ref<string | null>(null)
const selectedStatus = ref<RecordStatus | ''>('')
const reason = ref('')

const errors = reactive<Record<string, string>>({})

// Computed
const roleId = computed(() => route.params.id as string)

// Methods
const getStatusBadgeClass = (status?: RecordStatus) => {
  switch (status) {
    case 'Approved':
      return 'bg-green-100 text-green-800'
    case 'SecondApproved':
      return 'bg-blue-100 text-blue-800'
    case 'Rejected':
      return 'bg-red-100 text-red-800'
    case 'Unapproved':
    default:
      return 'bg-yellow-100 text-yellow-800'
  }
}

const validateForm = (): boolean => {
  // Clear previous errors
  Object.keys(errors).forEach(key => delete errors[key])

  let isValid = true

  if (!selectedStatus.value) {
    errors.status = 'Please select a new status'
    isValid = false
  }

  if (!reason.value.trim()) {
    errors.reason = 'Please provide a reason for the status change'
    isValid = false
  }

  return isValid
}

const handleStatusUpdate = async () => {
  if (!validateForm() || !role.value?.id) {
    return
  }

  // Show confirmation dialog
  const confirmed = await showConfirmDialog(
    'Confirm Status Change',
    `Are you sure you want to change the status of "${role.value.name}" to "${selectedStatus.value}"?`,
    'Yes, Update Status'
  )

  if (!confirmed) {
    return
  }

  try {
    isSubmitting.value = true
    submitError.value = null

    // Update role status
    await roleStore.updateRole(role.value.id, {
      status: selectedStatus.value as RecordStatus
    })

    // Update local role data
    role.value.status = selectedStatus.value as RecordStatus

    // Show success message
    await showSuccessAlert('Role status updated successfully!')

    // Navigate to role detail page
    router.push({
      name: 'admin.user-management.roles.detail',
      params: { id: role.value.id }
    })

  } catch (error: any) {
    submitError.value = error.message || 'Failed to update role status'
    console.error('Error updating role status:', error)
  } finally {
    isSubmitting.value = false
  }
}

const fetchRoleDetails = async () => {
  if (!roleId.value) {
    loadError.value = 'No role ID provided'
    return
  }

  try {
    isLoading.value = true
    loadError.value = null
    role.value = await roleStore.fetchRoleById(roleId.value)
  } catch (err: any) {
    loadError.value = err.message || 'Failed to load role details'
    console.error('Error fetching role details:', err)
  } finally {
    isLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  fetchRoleDetails()
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>
