<!--
  @fileoverview Protected Route Component
  @description Route-level protection with permission checking
-->

<template>
  <div v-if="hasAccess">
    <slot />
  </div>
  <div v-else-if="isAuthenticated" class="min-h-screen flex items-center justify-center bg-gray-50">
    <!-- Access Denied -->
    <div class="max-w-md w-full bg-white shadow-lg rounded-lg p-8 text-center">
      <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
        <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
        </svg>
      </div>
      
      <h1 class="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
      <p class="text-gray-600 mb-6">
        You don't have permission to access this page. Please contact your administrator if you believe this is an error.
      </p>
      
      <div class="space-y-3">
        <button
          @click="goBack"
          class="w-full bg-maneb-primary text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
        >
          Go Back
        </button>
        <button
          @click="goToDashboard"
          class="w-full bg-gray-200 text-gray-900 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"
        >
          Go to Dashboard
        </button>
      </div>

      <!-- Debug info in development -->
      <div v-if="isDev" class="mt-6 p-4 bg-gray-100 rounded-lg text-left">
        <h4 class="font-semibold text-sm text-gray-700 mb-2">Debug Info:</h4>
        <div class="text-xs text-gray-600 space-y-1">
          <div><strong>Current Role:</strong> {{ currentRole }}</div>
          <div><strong>Required Module:</strong> {{ module }}</div>
          <div><strong>Required Action:</strong> {{ action }}</div>
          <div><strong>Required Role:</strong> {{ role }}</div>
        </div>
      </div>
    </div>
  </div>
  <div v-else class="min-h-screen flex items-center justify-center bg-gray-50">
    <!-- Not Authenticated -->
    <div class="max-w-md w-full bg-white shadow-lg rounded-lg p-8 text-center">
      <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-4">
        <svg class="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
      </div>
      
      <h1 class="text-2xl font-bold text-gray-900 mb-2">Authentication Required</h1>
      <p class="text-gray-600 mb-6">
        Please log in to access this page.
      </p>
      
      <button
        @click="goToLogin"
        class="w-full bg-maneb-primary text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
      >
        Go to Login
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth.store'
import { usePermissions } from '@/composables/usePermissions'
import { PermissionModule, PermissionAction } from '@/utils/permissions'

interface Props {
  /** Module to check access for */
  module?: PermissionModule
  /** Specific action to check */
  action?: PermissionAction
  /** Multiple actions - user needs ANY of these */
  anyActions?: PermissionAction[]
  /** Multiple actions - user needs ALL of these */
  allActions?: PermissionAction[]
  /** Role name to check directly */
  role?: string
  /** Invert the permission check */
  not?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  not: false
})

const router = useRouter()
const authStore = useAuthStore()
const { can, canAccess, canAny, canAll, currentRole } = usePermissions()

/**
 * Check if user is authenticated
 */
const isAuthenticated = computed(() => authStore.isAuthenticated)

/**
 * Check if in development mode
 */
const isDev = computed(() => import.meta.env.DEV)

/**
 * Determine if user has access based on props
 */
const hasAccess = computed(() => {
  if (!isAuthenticated.value) return false

  let access = false

  // Direct role check
  if (props.role) {
    access = currentRole.value.toLowerCase() === props.role.toLowerCase()
  }
  // Module + specific action check
  else if (props.module && props.action) {
    access = can(props.module, props.action).value
  }
  // Module + any actions check
  else if (props.module && props.anyActions && props.anyActions.length > 0) {
    access = canAny(props.module, props.anyActions).value
  }
  // Module + all actions check
  else if (props.module && props.allActions && props.allActions.length > 0) {
    access = canAll(props.module, props.allActions).value
  }
  // Module access only
  else if (props.module) {
    access = canAccess(props.module).value
  }
  // Default to true if no specific requirements
  else {
    access = true
  }

  // Invert if requested
  return props.not ? !access : access
})

/**
 * Navigation methods
 */
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    goToDashboard()
  }
}

const goToDashboard = () => {
  const dashboardRoute = authStore.getDashboardRoute
  router.push(dashboardRoute)
}

const goToLogin = () => {
  router.push('/login')
}
</script>
